-- Add vault columns to existing users table
-- Run this in your Supabase SQL Editor

-- Add vault_name column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'vault_name') THEN
        ALTER TABLE users ADD COLUMN vault_name text DEFAULT 'My Vault';
    END IF;
END $$;

-- Add vault_banner_image column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'vault_banner_image') THEN
        ALTER TABLE users ADD COLUMN vault_banner_image text;
    END IF;
END $$;

-- Update existing users to have default vault names if they don't have one
UPDATE users 
SET vault_name = COALESCE(display_name, 'My Vault')
WHERE vault_name IS NULL OR vault_name = '';

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('vault_name', 'vault_banner_image')
ORDER BY column_name;
