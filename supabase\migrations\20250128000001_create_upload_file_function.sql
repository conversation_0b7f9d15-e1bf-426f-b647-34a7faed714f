/*
  # Fix RLS policies for Firebase authentication

  This migration adds RLS policies that allow anonymous users to insert
  block_contents if they can prove ownership through the related vault_blocks.
*/

-- Drop existing restrictive policy
DROP POLICY IF EXISTS "Users can manage own block contents" ON block_contents;

-- Create new policies that work with Firebase authentication
-- Policy for authenticated Supabase users (existing functionality)
CREATE POLICY "Authenticated users can manage own block contents"
  ON block_contents
  FOR ALL
  TO authenticated
  USING (auth.uid()::text = user_id);

-- Policy for anonymous users (Firebase authenticated) - SELECT
CREATE POLICY "Anonymous users can read own block contents"
  ON block_contents
  FOR SELECT
  TO anon
  USING (
    user_id IN (
      SELECT id FROM users WHERE id = user_id
    )
  );

-- Policy for anonymous users (Firebase authenticated) - INSERT
CREATE POLICY "Anonymous users can insert own block contents"
  ON block_contents
  FOR INSERT
  TO anon
  WITH CHECK (
    -- User must exist in users table
    user_id IN (SELECT id FROM users WHERE id = user_id)
    AND
    -- Block must exist and belong to the user
    block_id IN (
      SELECT id FROM vault_blocks
      WHERE id = block_id AND user_id = block_contents.user_id
    )
  );

-- Policy for anonymous users (Firebase authenticated) - UPDATE
CREATE POLICY "Anonymous users can update own block contents"
  ON block_contents
  FOR UPDATE
  TO anon
  USING (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  )
  WITH CHECK (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  );

-- Policy for anonymous users (Firebase authenticated) - DELETE
CREATE POLICY "Anonymous users can delete own block contents"
  ON block_contents
  FOR DELETE
  TO anon
  USING (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  );
