import React, { createContext, useContext, useState, useCallback } from 'react';
import { cn } from '../../lib/utils';

interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
}

interface ToastContextType {
  toasts: Toast[];
  toast: (toast: Omit<Toast, 'id'>) => void;
  dismiss: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 11);
    setToasts((prev) => [...prev, { ...toast, id }]);

    setTimeout(() => {
      setToasts((prev) => prev.filter((t) => t.id !== id));
    }, 4000);
  }, []);

  const dismiss = useCallback((id: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== id));
  }, []);

  const getToastStyles = (variant?: string) => {
    switch (variant) {
      case 'success':
        return {
          bg: 'bg-gradient-to-r from-emerald-900/90 to-green-800/90',
          border: 'border-emerald-400/30',
          icon: '✓',
          iconBg: 'bg-emerald-500/20',
          iconColor: 'text-emerald-300'
        };
      case 'destructive':
        return {
          bg: 'bg-gradient-to-r from-red-900/90 to-rose-800/90',
          border: 'border-red-400/30',
          icon: '✕',
          iconBg: 'bg-red-500/20',
          iconColor: 'text-red-300'
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-slate-800/90 to-gray-700/90',
          border: 'border-slate-400/30',
          icon: 'ℹ',
          iconBg: 'bg-slate-500/20',
          iconColor: 'text-slate-300'
        };
    }
  };

  return (
    <ToastContext.Provider value={{ toasts, toast, dismiss }}>
      {children}
      {/* Toast Container - Top Right */}
      <div className="fixed top-6 right-6 z-[100] space-y-3 pointer-events-none">
        {toasts.map((toast) => {
          const styles = getToastStyles(toast.variant);
          return (
            <div
              key={toast.id}
              className={cn(
                "pointer-events-auto relative overflow-hidden rounded-2xl backdrop-blur-md border shadow-2xl transition-all duration-500 ease-out transform",
                "animate-in slide-in-from-right-4 fade-in-0",
                "max-w-sm",
                styles.bg,
                styles.border
              )}
              style={{
                animation: 'slideInFromRight 0.5s ease-out forwards'
              }}
            >
              {/* Glassmorphic overlay */}
              <div className="absolute inset-0 bg-white/5 backdrop-blur-sm" />

              {/* Content */}
              <div className="relative p-4 flex items-start gap-3">
                {/* Icon */}
                <div className={cn(
                  "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                  styles.iconBg,
                  styles.iconColor
                )}>
                  {styles.icon}
                </div>

                {/* Text Content */}
                <div className="flex-1 min-w-0">
                  {toast.title && (
                    <div className="text-white font-semibold text-sm font-cormorant mb-1">
                      {toast.title}
                    </div>
                  )}
                  {toast.description && (
                    <div className="text-white/90 text-xs font-poppins leading-relaxed">
                      {toast.description}
                    </div>
                  )}
                </div>

                {/* Close Button */}
                <button
                  onClick={() => dismiss(toast.id)}
                  className="flex-shrink-0 text-white/60 hover:text-white transition-colors duration-200 p-1 rounded-full hover:bg-white/10"
                  aria-label="Close notification"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Progress bar */}
              <div className="absolute bottom-0 left-0 h-1 bg-white/20 w-full">
                <div
                  className="h-full bg-white/60 transition-all duration-[4000ms] ease-linear"
                  style={{
                    animation: 'progressBar 4s linear forwards'
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes slideInFromRight {
          from {
            opacity: 0;
            transform: translateX(100%);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes progressBar {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};