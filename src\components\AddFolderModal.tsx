import React, { useState } from "react";
import { Button } from "./ui/button";
import { X, Plus, Folder } from "lucide-react";

interface AddFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateFolder: (folderData: {
    name: string;
    color: string;
  }) => void;
}

const FOLDER_COLORS = [
  { name: "Blue", value: "from-blue-400 to-blue-600", preview: "bg-gradient-to-r from-blue-400 to-blue-600" },
  { name: "Green", value: "from-green-400 to-green-600", preview: "bg-gradient-to-r from-green-400 to-green-600" },
  { name: "Purple", value: "from-purple-400 to-purple-600", preview: "bg-gradient-to-r from-purple-400 to-purple-600" },
  { name: "Red", value: "from-red-400 to-red-600", preview: "bg-gradient-to-r from-red-400 to-red-600" },
  { name: "Orange", value: "from-orange-400 to-orange-600", preview: "bg-gradient-to-r from-orange-400 to-orange-600" },
  { name: "Pink", value: "from-pink-400 to-pink-600", preview: "bg-gradient-to-r from-pink-400 to-pink-600" },
  { name: "Teal", value: "from-teal-400 to-teal-600", preview: "bg-gradient-to-r from-teal-400 to-teal-600" },
  { name: "Indigo", value: "from-indigo-400 to-indigo-600", preview: "bg-gradient-to-r from-indigo-400 to-indigo-600" },
  { name: "Yellow", value: "from-yellow-400 to-yellow-600", preview: "bg-gradient-to-r from-yellow-400 to-yellow-600" },
  { name: "Gray", value: "from-gray-400 to-gray-600", preview: "bg-gradient-to-r from-gray-400 to-gray-600" },
];

export const AddFolderModal: React.FC<AddFolderModalProps> = ({
  isOpen,
  onClose,
  onCreateFolder
}) => {
  const [folderName, setFolderName] = useState("");
  const [selectedColor, setSelectedColor] = useState(FOLDER_COLORS[0].value);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleClose = () => {
    if (isSubmitting) return;
    setFolderName("");
    setSelectedColor(FOLDER_COLORS[0].value);
    onClose();
  };

  const handleSubmit = async () => {
    if (!folderName.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onCreateFolder({
        name: folderName.trim(),
        color: selectedColor
      });

      // Reset form
      setFolderName("");
      setSelectedColor(FOLDER_COLORS[0].value);
      onClose();
    } catch (error) {
      console.error('Error creating folder:', error);
      alert('Error creating folder. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
    if (e.key === 'Escape') {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
      <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-lg mx-4 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-cormorant font-bold text-[#2E0406]">Create New Folder</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
          >
            <X className="w-4 h-4 text-[#8b2635]" />
          </Button>
        </div>

        <div className="space-y-6">
          {/* Folder Name Input */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
              Folder Name
            </label>
            <input
              type="text"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Enter folder name..."
              className="w-full px-4 py-3 bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#8b2635]/20 focus:border-[#8b2635] transition-all duration-200 font-poppins text-[#2E0406] placeholder-[#8b7355]/60"
              autoFocus
            />
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-3">
              Folder Color
            </label>
            <div className="grid grid-cols-5 gap-3">
              {FOLDER_COLORS.map((color) => (
                <button
                  key={color.value}
                  onClick={() => setSelectedColor(color.value)}
                  className={`relative h-12 rounded-xl transition-all duration-200 ${color.preview} ${
                    selectedColor === color.value
                      ? 'ring-2 ring-[#8b2635] ring-offset-2 ring-offset-white/90 scale-105'
                      : 'hover:scale-105 hover:shadow-md'
                  }`}
                  title={color.name}
                >
                  {selectedColor === color.value && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-white rounded-full shadow-sm" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Preview */}
          <div>
            <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
              Preview
            </label>
            <div className="bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-xl overflow-hidden">
              {/* Preview Banner */}
              <div className="h-16 relative">
                <div className={`w-full h-full bg-gradient-to-r ${selectedColor}`} />
              </div>
              
              {/* Preview Content */}
              <div className="p-4">
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${selectedColor} flex items-center justify-center`}>
                    <Folder className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-cormorant font-bold text-[#2E0406] text-lg">
                      {folderName || "Folder Name"}
                    </h3>
                    <p className="text-[#8b7355] text-sm font-poppins">
                      Folder
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              onClick={handleClose}
              variant="outline"
              className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-10 font-poppins"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!folderName.trim() || isSubmitting}
              className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-10 font-poppins disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Folder
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
