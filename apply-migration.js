import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

const supabaseUrl = 'https://zyijbgtqthkbbtprkkod.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5aWpiZ3RxdGhrYmJ0cHJra29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Mzg1MTAsImV4cCI6MjA2OTAxNDUxMH0.jHpQv8AKlBcsl3B85et6mc4R5U6Z-1co-NK6eDc8upg';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function applyMigration() {
  try {
    console.log('Reading migration file...');
    const migrationSQL = fs.readFileSync('supabase/migrations/20250128000001_create_upload_file_function.sql', 'utf8');
    
    console.log('Applying migration...');
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.error('Error applying migration:', error);
    } else {
      console.log('Migration applied successfully!', data);
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

applyMigration();
