/*
  # Add status column to block_contents table
  
  The block_contents table needs a status column to differentiate between
  published content and drafts.
*/

-- Add status column to block_contents table
ALTER TABLE block_contents 
ADD COLUMN IF NOT EXISTS status text DEFAULT 'published' 
CHECK (status IN ('published', 'draft'));

-- Create index for status column
CREATE INDEX IF NOT EXISTS idx_block_contents_status ON block_contents(status);
CREATE INDEX IF NOT EXISTS idx_block_contents_block_status ON block_contents(block_id, status);
