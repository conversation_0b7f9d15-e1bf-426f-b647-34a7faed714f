import { supabase } from './supabase';
import { ensureTagDetectionRulesTable } from './initializeDatabase';

// Interface for tag detection rules
interface TagRule {
  id: string;
  tag_name: string;
  keywords: string[];
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Cache for tag rules to avoid frequent database calls
let tagRulesCache: TagRule[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Flag to track if database is available
let isDatabaseAvailable = true;

// Load tag rules from database
const loadTagRules = async (): Promise<TagRule[]> => {
  try {
    // Check if cache is still valid
    const now = Date.now();
    if (tagRulesCache && (now - cacheTimestamp) < CACHE_DURATION) {
      return tagRulesCache;
    }

    console.log('Loading tag detection rules from database...');

    const { data: rules, error } = await supabase
      .from('tag_detection_rules')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false });

    if (error) {
      console.error('Error loading tag rules from database:', error);
      isDatabaseAvailable = false;
      return [];
    }

    if (!rules || rules.length === 0) {
      console.warn('No tag detection rules found in database');
      return [];
    }

    // Update cache
    tagRulesCache = rules;
    cacheTimestamp = now;
    isDatabaseAvailable = true;

    console.log(`Loaded ${rules.length} tag detection rules from database`);
    return rules;
  } catch (error) {
    console.error('Error in loadTagRules:', error);
    isDatabaseAvailable = false;
    return [];
  }
};

// Smart tag detection using database rules
export const detectSmartTag = async (blockName: string): Promise<string> => {
  if (!blockName || !blockName.trim()) {
    return 'General';
  }

  try {
    // Load rules from database
    const rules = await loadTagRules();

    if (!rules || rules.length === 0) {
      console.warn('No tag detection rules available');
      return 'General';
    }

    const name = blockName.toLowerCase().trim();

    // Find the first matching rule (rules are ordered by priority)
    for (const rule of rules) {
      if (rule && rule.keywords && Array.isArray(rule.keywords)) {
        for (const keyword of rule.keywords) {
          if (keyword && typeof keyword === 'string') {
            const keywordLower = keyword.toLowerCase();
            // Check for exact word match or partial match for compound words
            if (name === keywordLower ||
                name.includes(keywordLower) ||
                keywordLower.includes(name) ||
                name.split(' ').some(word => word === keywordLower) ||
                keywordLower.split(' ').some(word => word === name)) {
              console.log(`Detected tag "${rule.tag_name}" for "${blockName}" using keyword "${keyword}"`);
              return rule.tag_name || 'General';
            }
          }
        }
      }
    }

    console.log(`No matching tag found for "${blockName}", using General`);
    return 'General';
  } catch (error) {
    console.error('Error in smart tag detection:', error);
    return 'General';
  }
};
// Synchronous version for immediate UI feedback (uses cache)
export const detectSmartTagSync = (blockName: string): string => {
  try {
    if (!blockName || !blockName.trim()) {
      return 'General';
    }

    // Use cached rules if available
    const rules = tagRulesCache || [];

    if (rules.length === 0) {
      console.warn('No cached tag detection rules available');
      return 'General';
    }

    const name = blockName.toLowerCase().trim();

    // Find the first matching rule (rules are ordered by priority)
    for (const rule of rules) {
      if (rule && rule.keywords && Array.isArray(rule.keywords)) {
        for (const keyword of rule.keywords) {
          if (keyword && typeof keyword === 'string') {
            const keywordLower = keyword.toLowerCase();
            // Check for exact word match or partial match for compound words
            if (name === keywordLower ||
                name.includes(keywordLower) ||
                keywordLower.includes(name) ||
                name.split(' ').some(word => word === keywordLower) ||
                keywordLower.split(' ').some(word => word === name)) {
              console.log(`Sync detected tag "${rule.tag_name}" for "${blockName}" using keyword "${keyword}"`);
              return rule.tag_name || 'General';
            }
          }
        }
      }
    }

    console.log(`No matching tag found for "${blockName}" (sync), using General`);
    return 'General';
  } catch (error) {
    console.error('Error in detectSmartTagSync:', error);
    return 'General';
  }
};



// Get all available tag names from database
export const getAvailableTags = async (): Promise<string[]> => {
  try {
    const rules = await loadTagRules();
    const uniqueTags = [...new Set(rules.map(rule => rule.tag_name))];
    return uniqueTags.sort();
  } catch (error) {
    console.error('Error getting available tags:', error);
    return ['General'];
  }
};

// Get available tags synchronously from cache
export const getAvailableTagsSync = (): string[] => {
  try {
    if (!tagRulesCache || tagRulesCache.length === 0) {
      return ['General'];
    }
    const uniqueTags = [...new Set(tagRulesCache.map(rule => rule.tag_name))];
    return uniqueTags.sort();
  } catch (error) {
    console.error('Error getting available tags (sync):', error);
    return ['General'];
  }
};

// Clear cache (useful for admin updates)
export const clearTagRulesCache = (): void => {
  tagRulesCache = null;
  cacheTimestamp = 0;
};

// Add a new tag rule
export const addTagRule = async (rule: Omit<TagRule, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('tag_detection_rules')
      .insert([rule]);

    if (error) {
      console.error('Error adding tag rule:', error);
      return false;
    }

    // Clear cache to force reload
    clearTagRulesCache();
    return true;
  } catch (error) {
    console.error('Error adding tag rule:', error);
    return false;
  }
};

// Update an existing tag rule
export const updateTagRule = async (id: string, updates: Partial<TagRule>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('tag_detection_rules')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error updating tag rule:', error);
      return false;
    }

    // Clear cache to force reload
    clearTagRulesCache();
    return true;
  } catch (error) {
    console.error('Error updating tag rule:', error);
    return false;
  }
};
