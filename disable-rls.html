<!DOCTYPE html>
<html>
<head>
    <title>Disable RLS</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Disable RLS for Testing</h1>
    <button onclick="disableRLS()">Disable RLS</button>
    <button onclick="enableRLS()">Enable RLS</button>
    <button onclick="testInsert()">Test Insert</button>
    <div id="output"></div>

    <script>
        const supabaseUrl = 'https://zyijbgtqthkbbtprkkod.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5aWpiZ3RxdGhrYmJ0cHJra29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Mzg1MTAsImV4cCI6MjA2OTAxNDUxMH0.jHpQv8AKlBcsl3B85et6mc4R5U6Z-1co-NK6eDc8upg';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabase<PERSON>nonKey);

        async function disableRLS() {
            try {
                const { data, error } = await supabase.rpc('exec_sql', { 
                    sql: 'ALTER TABLE block_contents DISABLE ROW LEVEL SECURITY;' 
                });
                document.getElementById('output').innerHTML = `<pre>Disable RLS Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }

        async function enableRLS() {
            try {
                const { data, error } = await supabase.rpc('exec_sql', { 
                    sql: 'ALTER TABLE block_contents ENABLE ROW LEVEL SECURITY;' 
                });
                document.getElementById('output').innerHTML = `<pre>Enable RLS Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }

        async function testInsert() {
            try {
                const { data, error } = await supabase
                    .from('block_contents')
                    .insert([{
                        block_id: '00000000-0000-0000-0000-000000000000',
                        user_id: 'test-user',
                        title: 'test.txt',
                        content: 'test content',
                        content_type: 'text',
                        position: 0
                    }])
                    .select()
                    .single();
                document.getElementById('output').innerHTML = `<pre>Test Insert Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }
    </script>
</body>
</html>
