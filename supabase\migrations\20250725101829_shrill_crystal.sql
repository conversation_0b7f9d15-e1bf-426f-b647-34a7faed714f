/*
  # Create users table for Kairo authentication

  1. New Tables
    - `users`
      - `id` (uuid, primary key) - matches Firebase UID
      - `auth_provider` (enum) - email, google, or facebook
      - `email` (text, unique, not null)
      - `password_hash` (text) - only for email users
      - `display_name` (text)
      - `username` (text, unique)
      - `profile_pic_url` (text)
      - `bio` (text)
      - `xp` (integer, default 0) - for gamification
      - `level` (integer, default 1) - for gamification
      - `theme` (text, default 'dark-crimson')
      - `google_id` (text, unique) - for Google OAuth
      - `facebook_id` (text, unique) - for Facebook OAuth
      - `created_at` (timestamp, default now)
      - `updated_at` (timestamp, default now)

  2. Security
    - Enable RLS on `users` table
    - Add policy for users to read/update their own data
    - Add policy for authenticated users to read public profile data
*/

-- Create enum for auth providers
CREATE TYPE auth_provider_enum AS ENUM ('email', 'google', 'facebook');

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY,
  auth_provider auth_provider_enum NOT NULL,
  email text UNIQUE NOT NULL,
  password_hash text,
  display_name text,
  username text UNIQUE,
  profile_pic_url text,
  bio text,
  xp integer DEFAULT 0,
  level integer DEFAULT 1,
  theme text DEFAULT 'dark-crimson',
  google_id text UNIQUE,
  facebook_id text UNIQUE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can read and update their own data
CREATE POLICY "Users can manage own data"
  ON users
  FOR ALL
  TO authenticated
  USING (auth.uid()::text = id::text);

-- Policy: Users can read public profile data of other users
CREATE POLICY "Users can read public profiles"
  ON users
  FOR SELECT
  TO authenticated
  USING (true);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_facebook_id ON users(facebook_id);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON users(auth_provider);