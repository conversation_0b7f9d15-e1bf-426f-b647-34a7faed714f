{"version": "1.0.0", "source": "./index.html", "type": "module", "name": "anima-project", "description": "A React project automatically generated by <PERSON><PERSON> using the Shadcn UI library", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@supabase/supabase-js": "^2.52.1", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "firebase": "^12.0.0", "fuse.js": "^7.1.0", "lucide-react": "^0.453.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.61.1", "react-router-dom": "^6.30.1", "tailwind-merge": "2.5.4", "tailwindcss-animate": "1.0.7", "zod": "^4.0.10"}, "devDependencies": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "4.3.4", "esbuild": "0.24.0", "globals": "15.12.0", "tailwindcss": "3.4.16", "typescript": "^5.0.0", "vite": "6.0.4"}}