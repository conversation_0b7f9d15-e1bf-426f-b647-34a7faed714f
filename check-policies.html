<!DOCTYPE html>
<html>
<head>
    <title>Check RLS Policies</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Check and Fix RLS Policies</h1>
    <button onclick="checkPolicies()">Check Current Policies</button>
    <button onclick="fixPolicies()">Fix RLS Policies</button>
    <button onclick="testInsert()">Test Insert</button>
    <div id="output"></div>

    <script>
        const supabaseUrl = 'https://zyijbgtqthkbbtprkkod.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5aWpiZ3RxdGhrYmJ0cHJra29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Mzg1MTAsImV4cCI6MjA2OTAxNDUxMH0.jHpQv8AKlBcsl3B85et6mc4R5U6Z-1co-NK6eDc8upg';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);

        async function checkPolicies() {
            try {
                const { data, error } = await supabase.rpc('exec_sql', { 
                    sql: `
                        SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
                        FROM pg_policies 
                        WHERE tablename = 'block_contents';
                    ` 
                });
                document.getElementById('output').innerHTML = `<pre>Current Policies: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }

        async function fixPolicies() {
            const fixSQL = `
-- Drop all existing policies for block_contents
DROP POLICY IF EXISTS "Users can manage own block contents" ON block_contents;
DROP POLICY IF EXISTS "Authenticated users can manage own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can read own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can insert own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can update own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can delete own block contents" ON block_contents;

-- Create a simple policy that allows all operations for anonymous users
-- if the user exists in the users table and owns the block
CREATE POLICY "Allow all operations for valid users"
  ON block_contents
  FOR ALL
  TO anon, authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.id = block_contents.user_id
    )
    AND
    EXISTS (
      SELECT 1 FROM vault_blocks vb 
      WHERE vb.id = block_contents.block_id 
      AND vb.user_id = block_contents.user_id
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.id = block_contents.user_id
    )
    AND
    EXISTS (
      SELECT 1 FROM vault_blocks vb 
      WHERE vb.id = block_contents.block_id 
      AND vb.user_id = block_contents.user_id
    )
  );
            `;

            try {
                const { data, error } = await supabase.rpc('exec_sql', { sql: fixSQL });
                document.getElementById('output').innerHTML = `<pre>Fix Policies Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }

        async function testInsert() {
            try {
                // First, let's get a real user and block from the database
                const { data: users, error: userError } = await supabase
                    .from('users')
                    .select('id')
                    .limit(1);
                
                if (userError || !users || users.length === 0) {
                    document.getElementById('output').innerHTML = `<pre>No users found: ${JSON.stringify(userError, null, 2)}</pre>`;
                    return;
                }

                const userId = users[0].id;

                const { data: blocks, error: blockError } = await supabase
                    .from('vault_blocks')
                    .select('id')
                    .eq('user_id', userId)
                    .limit(1);

                if (blockError || !blocks || blocks.length === 0) {
                    document.getElementById('output').innerHTML = `<pre>No blocks found for user: ${JSON.stringify(blockError, null, 2)}</pre>`;
                    return;
                }

                const blockId = blocks[0].id;

                // Now try to insert
                const { data, error } = await supabase
                    .from('block_contents')
                    .insert([{
                        block_id: blockId,
                        user_id: userId,
                        title: 'test.txt',
                        content: 'test content',
                        content_type: 'text',
                        position: 0
                    }])
                    .select()
                    .single();
                
                document.getElementById('output').innerHTML = `<pre>Test Insert Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }
    </script>
</body>
</html>
