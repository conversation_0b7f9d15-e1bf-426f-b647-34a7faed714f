/*
  # Add archive content type to block_contents table
  
  This migration adds 'archive' as a valid content type for the block_contents table
  to support zip files, compressed folders, and other archive formats.
*/

-- Drop the existing constraint
ALTER TABLE block_contents DROP CONSTRAINT IF EXISTS block_contents_content_type_check;

-- Add the new constraint with 'archive' included
ALTER TABLE block_contents ADD CONSTRAINT block_contents_content_type_check 
CHECK (
  content_type = ANY (
    ARRAY[
      'text'::text,
      'image'::text,
      'video'::text,
      'audio'::text,
      'file'::text,
      'archive'::text
    ]
  )
);
