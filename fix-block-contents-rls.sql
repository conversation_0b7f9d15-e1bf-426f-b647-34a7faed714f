-- Fix RLS policies for block_contents table
-- This script should be run in the Supabase SQL Editor

-- First, check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'block_contents';

-- Check current policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'block_contents';

-- Drop all existing policies for block_contents
DROP POLICY IF EXISTS "Users can manage own block contents" ON block_contents;
DROP POLICY IF EXISTS "Authenticated users can manage own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can read own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can insert own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can update own block contents" ON block_contents;
DROP POLICY IF EXISTS "Anonymous users can delete own block contents" ON block_contents;
DROP POLICY IF EXISTS "Allow all operations for valid users" ON block_contents;

-- Check what policies vault_blocks has (since it works)
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'vault_blocks';

-- Option 1: Disable RLS temporarily for testing
-- ALTER TABLE block_contents DISABLE ROW LEVEL SECURITY;

-- Option 2: Create a permissive policy similar to vault_blocks
-- First, let's see if vault_blocks has RLS enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'vault_blocks';

-- Option 3: Create a simple policy that allows operations for any user that exists
CREATE POLICY "Allow operations for existing users"
  ON block_contents
  FOR ALL
  TO anon, authenticated
  USING (
    user_id IN (SELECT id FROM users)
  )
  WITH CHECK (
    user_id IN (SELECT id FROM users)
    AND
    block_id IN (SELECT id FROM vault_blocks WHERE user_id = block_contents.user_id)
  );

-- Test the policy by checking if we can select from block_contents
SELECT COUNT(*) FROM block_contents;

-- If the above works, test an insert (you'll need to replace with real IDs)
-- INSERT INTO block_contents (block_id, user_id, title, content, content_type, position)
-- VALUES (
--   (SELECT id FROM vault_blocks LIMIT 1),
--   (SELECT user_id FROM vault_blocks LIMIT 1),
--   'test-file.txt',
--   'test content',
--   'text',
--   0
-- );
