import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { Input } from "../../components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "../../components/ui/tabs";
import { useAuth } from "../../hooks/useAuth";
import { useToast } from "../../components/ui/toast";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { Eye, EyeOff } from "lucide-react";

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const registerSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(6, "Password must be at least 6 characters"),
  displayName: z.string().min(2, "Display name must be at least 2 characters"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type LoginForm = z.infer<typeof loginSchema>;
type RegisterForm = z.infer<typeof registerSchema>;
type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;

export const Login = (): JSX.Element => {
  const [activeTab, setActiveTab] = useState("login");
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showLoginPassword, setShowLoginPassword] = useState(false);
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [fullScreenLoading, setFullScreenLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("loading...");
  const { signInWithEmail, signUpWithEmail, signInWithGoogle, signInWithFacebook, resetPassword, loading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const loginForm = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: { email: "", password: "" }
  });

  const registerForm = useForm<RegisterForm>({
    resolver: zodResolver(registerSchema),
    defaultValues: { email: "", password: "", confirmPassword: "", displayName: "" }
  });

  const forgotPasswordForm = useForm<ForgotPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: { email: "" }
  });

  // Load saved credentials on component mount
  useEffect(() => {
    const savedEmail = localStorage.getItem('kairo_saved_email');
    const savedPassword = localStorage.getItem('kairo_saved_password');
    const wasRemembered = localStorage.getItem('kairo_remember_me') === 'true';

    if (savedEmail && savedPassword && wasRemembered) {
      loginForm.setValue('email', savedEmail);
      loginForm.setValue('password', savedPassword);
      setRememberMe(true);
    }
  }, [loginForm]);

  const onLogin = async (data: LoginForm) => {
    try {
      setAuthError(null); // Clear any previous errors
      setLoadingMessage("signing you in...");
      setFullScreenLoading(true);

      await signInWithEmail(data.email, data.password);

      // Handle remember me functionality
      if (rememberMe) {
        localStorage.setItem('kairo_saved_email', data.email);
        localStorage.setItem('kairo_saved_password', data.password);
        localStorage.setItem('kairo_remember_me', 'true');
      } else {
        // Clear saved credentials if remember me is not checked
        localStorage.removeItem('kairo_saved_email');
        localStorage.removeItem('kairo_saved_password');
        localStorage.removeItem('kairo_remember_me');
      }

      toast({
        title: "Success!",
        description: "Welcome back to Kairo.",
        variant: "success"
      });

      // Add a longer delay for smooth transition
      setTimeout(() => {
        setFullScreenLoading(false);
        navigate('/home');
      }, 3000);
    } catch (error: any) {
      setFullScreenLoading(false);
      // Set error message to display under the form
      let errorMessage = "Login failed. Please try again.";

      if (error.message.includes("user-not-found")) {
        errorMessage = "No account found with this email address.";
      } else if (error.message.includes("wrong-password") || error.message.includes("invalid-credential")) {
        errorMessage = "Invalid email or password.";
      } else if (error.message.includes("too-many-requests")) {
        errorMessage = "Too many failed attempts. Please try again later.";
      } else if (error.message.includes("user-disabled")) {
        errorMessage = "This account has been disabled.";
      }

      setAuthError(errorMessage);
    }
  };

  const onRegister = async (data: RegisterForm) => {
    try {
      setAuthError(null); // Clear any previous errors
      setLoadingMessage("creating your account...");
      setFullScreenLoading(true);

      await signUpWithEmail(data.email, data.password, data.displayName);
      toast({
        title: "Account Created!",
        description: "Welcome to Kairo. Your account has been created successfully.",
        variant: "success"
      });

      // Add a longer delay for smooth transition
      setTimeout(() => {
        setFullScreenLoading(false);
        navigate('/home');
      }, 3000);
    } catch (error: any) {
      setFullScreenLoading(false);
      // Set error message to display under the form
      let errorMessage = "Registration failed. Please try again.";

      if (error.message.includes("email-already-in-use")) {
        errorMessage = "An account with this email already exists.";
      } else if (error.message.includes("weak-password")) {
        errorMessage = "Password is too weak. Please choose a stronger password.";
      } else if (error.message.includes("invalid-email")) {
        errorMessage = "Please enter a valid email address.";
      }

      setAuthError(errorMessage);
    }
  };

  const onForgotPassword = async (data: ForgotPasswordForm) => {
    try {
      await resetPassword(data.email);
      toast({
        title: "Reset Email Sent",
        description: "Check your email for password reset instructions.",
        variant: "success"
      });
      setShowForgotPassword(false);
    } catch (error: any) {
      toast({
        title: "Reset Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setAuthError(null);
      setLoadingMessage("signing in with google...");
      setFullScreenLoading(true);

      await signInWithGoogle();
      toast({
        title: "Success!",
        description: "Welcome to Kairo.",
        variant: "success"
      });

      // Add a longer delay for smooth transition
      setTimeout(() => {
        setFullScreenLoading(false);
        navigate('/home');
      }, 3000);
    } catch (error: any) {
      setFullScreenLoading(false);
      setAuthError("Google login failed. Please try again.");
    }
  };

  const handleFacebookLogin = async () => {
    try {
      setAuthError(null);
      setLoadingMessage("signing in with facebook...");
      setFullScreenLoading(true);

      await signInWithFacebook();
      toast({
        title: "Success!",
        description: "Welcome to Kairo.",
        variant: "success"
      });

      // Add a longer delay for smooth transition
      setTimeout(() => {
        setFullScreenLoading(false);
        navigate('/home');
      }, 3000);
    } catch (error: any) {
      setFullScreenLoading(false);
      setAuthError("Facebook login failed. Please try again.");
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setAuthError(null); // Clear errors when switching tabs
  };

  return (
    <>
      {fullScreenLoading && <LoadingScreen message={loadingMessage} />}
      <div className="bg-[#1e0202] min-h-screen w-full font-sans flex items-center justify-center p-4">
      <div className="w-full max-w-6xl h-[700px] relative overflow-hidden">
        {/* Image Panel */}
        <div
          className={`absolute h-full rounded-[5%] overflow-hidden shadow-2xl transition-all duration-[8000ms] ease-in-out ${
            activeTab === 'login'
              ? 'left-0'
              : 'left-1/2'
          }`}
          style={{
            width: 'calc(50% - 6px)',
          }}
        >
          <img
            className="w-full h-full object-cover"
            alt="Dark red textured background"
            src="https://i.pinimg.com/736x/0d/60/85/0d60852201c816c711690f5792d0a2a2.jpg"
          />
        </div>

        {/* Glassmorphic Login panel */}
        <div
          className={`absolute h-full backdrop-blur-md border border-white/30 rounded-3xl shadow-2xl transition-all duration-[8000ms] ease-in-out ${
            activeTab === 'login'
              ? 'right-0'
              : 'left-0'
          }`}
          style={{
            width: 'calc(50% - 6px)',
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0.75) 100%)',
            backdropFilter: 'blur(15px)',
            WebkitBackdropFilter: 'blur(15px)',
            boxShadow: '0 25px 45px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
          }}
        >
          {/* Subtle inner glassmorphic overlay */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent opacity-30" />

          <div className="absolute inset-0 flex flex-col items-center justify-start pt-12 px-16 relative z-10 transition-all duration-[8000ms] ease-in-out">
          {/* App Logo - Fixed position to prevent glitching */}
          <div className="text-center mb-4 w-full max-w-sm">
            <div className="w-16 h-16 mx-auto mb-3 flex items-center justify-center">
              <img
                src="/app logo.png"
                alt="Kairo App Logo"
                // className="w-12 h-12 rounded-xl object-cover shadow-lg"
              />
            </div>
            <h2 className="text-3xl mb-2 text-black font-cormorant font-bold">welcome to kairo.</h2>
            <p className="text-sm text-center mb-4 text-black/70 font-poppins">
              enter your sanctuary of thought.
              <br />
              {showForgotPassword
                ? "reset your password."
                : activeTab === "login"
                  ? "sign in to continue."
                  : "register to continue."
              }
            </p>
          </div>

          {!showForgotPassword ? (
            <>
              {/* Login/Register tabs with outer cover */}
              <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full max-w-sm mb-4">
                <TabsList className="grid w-full grid-cols-2 mb-4 bg-gray-200 p-1 rounded-lg h-14">
                  <TabsTrigger
                    value="login"
                    className="bg-transparent text-black data-[state=active]:bg-[#3d0909] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-md font-poppins h-full py-3 text-sm font-medium m-0 border-0"
                  >
                    login
                  </TabsTrigger>
                  <TabsTrigger
                    value="register"
                    className="bg-transparent text-black data-[state=active]:bg-[#3d0909] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-md font-poppins h-full py-3 text-sm font-medium m-0 border-0"
                  >
                    register
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="login" className="w-full max-w-sm mx-auto">
                  <Card className="border-0 shadow-none w-full bg-transparent">
                    <CardContent className="p-0 space-y-3">
                      <form onSubmit={loginForm.handleSubmit(onLogin)} className="space-y-3">
                        <div>
                          <Input
                            {...loginForm.register("email")}
                            placeholder="email address"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 transition-all duration-200"
                            disabled={loading}
                            aria-label="Email address"
                            aria-invalid={!!loginForm.formState.errors.email}
                            aria-describedby={loginForm.formState.errors.email ? "email-error" : undefined}
                            onChange={(e) => {
                              loginForm.register("email").onChange(e);
                              if (authError) setAuthError(null); // Clear auth error when user starts typing
                            }}
                          />
                          {loginForm.formState.errors.email && (
                            <p id="email-error" className="text-red-600 text-xs mt-1 font-poppins" role="alert">{loginForm.formState.errors.email.message}</p>
                          )}
                        </div>
                        <div className="relative">
                          <Input
                            {...loginForm.register("password")}
                            type={showLoginPassword ? "text" : "password"}
                            placeholder="password"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 pr-12 transition-all duration-200"
                            disabled={loading}
                            aria-label="Password"
                            aria-invalid={!!loginForm.formState.errors.password}
                            aria-describedby={loginForm.formState.errors.password ? "password-error" : undefined}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-12 px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowLoginPassword(!showLoginPassword)}
                            disabled={loading}
                          >
                            {showLoginPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-600" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-600" />
                            )}
                          </Button>
                          {loginForm.formState.errors.password && (
                            <p id="password-error" className="text-red-600 text-xs mt-1 font-poppins" role="alert">{loginForm.formState.errors.password.message}</p>
                          )}
                        </div>

                        {/* Remember Me and Forgot Password */}
                        <div className="flex items-center justify-between mb-1">
                          <label className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={rememberMe}
                              onChange={(e) => setRememberMe(e.target.checked)}
                              className="w-4 h-4 text-[#3d0909] bg-white/40 border-white/50 rounded focus:ring-[#3d0909]/30 focus:ring-2 backdrop-blur-sm"
                              disabled={loading}
                            />
                            <span className="text-xs text-black/70 font-poppins">remember me</span>
                          </label>
                          <Button
                            type="button"
                            variant="link"
                            className="text-xs p-0 h-auto text-black/70 hover:text-black font-poppins"
                            onClick={() => setShowForgotPassword(true)}
                          >
                            forgot password?
                          </Button>
                        </div>
                        <Button
                          type="submit"
                          className="w-full bg-[#3d0909] hover:bg-[#2d0707] text-white h-12 rounded-lg transition-all duration-200 font-poppins text-sm font-medium"
                          disabled={loading}
                        >
                          {loading ? "signing in..." : "proceed"}
                        </Button>

                        {/* Authentication Error Display */}
                        {authError && activeTab === "login" && (
                          <div className="mt-3 p-3 bg-red-100/60 backdrop-blur-sm border border-red-300/50 rounded-lg">
                            <p className="text-red-700 text-xs font-poppins text-center">{authError}</p>
                          </div>
                        )}
                      </form>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="register" className="w-full max-w-sm mx-auto">
                  <Card className="border-0 shadow-none w-full bg-transparent">
                    <CardContent className="p-0 space-y-2">
                      <form onSubmit={registerForm.handleSubmit(onRegister)} className="space-y-2">
                        <div>
                          <Input
                            {...registerForm.register("displayName")}
                            placeholder="full name"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 transition-all duration-200"
                            disabled={loading}
                          />
                          {registerForm.formState.errors.displayName && (
                            <p className="text-red-600 text-xs mt-1 font-poppins">{registerForm.formState.errors.displayName.message}</p>
                          )}
                        </div>
                        <div>
                          <Input
                            {...registerForm.register("email")}
                            placeholder="email address"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 transition-all duration-200"
                            disabled={loading}
                          />
                          {registerForm.formState.errors.email && (
                            <p className="text-red-600 text-xs mt-1 font-poppins">{registerForm.formState.errors.email.message}</p>
                          )}
                        </div>
                        <div className="relative">
                          <Input
                            {...registerForm.register("password")}
                            type={showRegisterPassword ? "text" : "password"}
                            placeholder="password"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 pr-12 transition-all duration-200"
                            disabled={loading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-12 px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowRegisterPassword(!showRegisterPassword)}
                            disabled={loading}
                          >
                            {showRegisterPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-600" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-600" />
                            )}
                          </Button>
                          {registerForm.formState.errors.password && (
                            <p className="text-red-600 text-xs mt-1 font-poppins">{registerForm.formState.errors.password.message}</p>
                          )}
                        </div>
                        <div className="relative">
                          <Input
                            {...registerForm.register("confirmPassword")}
                            type={showConfirmPassword ? "text" : "password"}
                            placeholder="confirm password"
                            className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 pr-12 transition-all duration-200"
                            disabled={loading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-12 px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            disabled={loading}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-600" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-600" />
                            )}
                          </Button>
                          {registerForm.formState.errors.confirmPassword && (
                            <p className="text-red-600 text-xs mt-1 font-poppins">{registerForm.formState.errors.confirmPassword.message}</p>
                          )}
                        </div>
                        <Button
                          type="submit"
                          className="w-full bg-[#3d0909] hover:bg-[#2d0707] text-white h-10 rounded-lg transition-all duration-200 font-poppins text-sm font-medium mt-2"
                          disabled={loading}
                        >
                          {loading ? "creating account..." : "proceed"}
                        </Button>

                        {/* Authentication Error Display */}
                        {authError && activeTab === "register" && (
                          <div className="mt-3 p-3 bg-red-100/60 backdrop-blur-sm border border-red-300/50 rounded-lg">
                            <p className="text-red-700 text-xs font-poppins text-center">{authError}</p>
                          </div>
                        )}
                      </form>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              <div className="text-center text-sm mt-2 w-full max-w-sm mx-auto">
                <p className="text-black/70 font-poppins text-xs">
                  {activeTab === "login" ? "don't have an account? " : "already have an account? "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-black hover:text-black/70 font-poppins text-xs underline"
                    onClick={() => setActiveTab(activeTab === "login" ? "register" : "login")}
                  >
                    {activeTab === "login" ? "sign up" : "sign in"}
                  </Button>
                </p>
                <p className="mt-2 text-black/70 font-poppins text-xs">or continue with</p>

                <div className="flex justify-center gap-3 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="rounded-full w-8 h-8 bg-black text-white border-0 hover:bg-gray-800 transition-all duration-200 font-poppins text-sm"
                    onClick={handleGoogleLogin}
                    disabled={loading}
                    aria-label="Sign in with Google"
                  >
                    G
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="rounded-full w-8 h-8 bg-black text-white border-0 hover:bg-gray-800 transition-all duration-200 font-poppins text-sm"
                    onClick={handleFacebookLogin}
                    disabled={loading}
                    aria-label="Sign in with Facebook"
                  >
                    f
                  </Button>
                </div>
              </div>
            </>
          ) : (
            /* Forgot Password Form */
            <Card className="border-0 shadow-none w-full max-w-sm mx-auto bg-transparent">
              <CardContent className="p-0 space-y-3">
                <form onSubmit={forgotPasswordForm.handleSubmit(onForgotPassword)} className="space-y-3">
                  <div>
                    <Input
                      {...forgotPasswordForm.register("email")}
                      placeholder="email address"
                      className="bg-white/40 backdrop-blur-sm border border-white/50 h-12 rounded-lg text-black placeholder:text-gray-600 font-poppins focus:ring-2 focus:ring-[#3d0909]/30 focus:border-[#3d0909] focus:bg-white/60 text-sm px-4 transition-all duration-200"
                      disabled={loading}
                    />
                    {forgotPasswordForm.formState.errors.email && (
                      <p className="text-red-600 text-xs mt-1 font-poppins">{forgotPasswordForm.formState.errors.email.message}</p>
                    )}
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-[#3d0909] hover:bg-[#2d0707] text-white h-12 rounded-lg transition-all duration-200 font-poppins text-sm font-medium"
                    disabled={loading}
                  >
                    {loading ? "sending..." : "send reset email"}
                  </Button>
                  <Button
                    type="button"
                    variant="link"
                    className="w-full text-black/70 hover:text-black font-poppins text-sm"
                    onClick={() => setShowForgotPassword(false)}
                  >
                    back to login
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};