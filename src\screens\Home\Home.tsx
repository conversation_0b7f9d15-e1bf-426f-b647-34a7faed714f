import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { useAuth } from "../../hooks/useAuth";
import { useToast } from "../../components/ui/toast";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { supabase } from "../../lib/supabase";
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  Zap,
  Star,
  Target,
  BookOpen,
  Calendar,
  Plus,
  FileText,
  HelpCircle,
  RefreshCw,
  TrendingUp,
  Award,
  Clock,
  Eye,
  Check,
  Trash2
} from "lucide-react";

export const Home = (): JSX.Element => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentTip, setCurrentTip] = useState(0);
  const [showTipInHero, setShowTipInHero] = useState(true);

  const [vaultName, setVaultName] = useState("my vault");
  const [userSettings, setUserSettings] = useState<any>(null);

  const [todos, setTodos] = useState([
    { id: 1, text: "Complete React fundamentals", completed: false },
    { id: 2, text: "Study TypeScript basics", completed: true },
    { id: 3, text: "Build a portfolio project", completed: false }
  ]);
  const [newTodoText, setNewTodoText] = useState("");
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);

  // Load user settings from users table
  const loadUserSettings = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.uid)
        .single();

      if (error) {
        console.error('Error loading user data:', error);
        return;
      }

      if (data) {
        setUserSettings(data);
        // Use vault_name if available, otherwise fall back to display_name or default
        setVaultName(data.vault_name || data.display_name || 'my vault');
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  // Load user settings when user changes
  useEffect(() => {
    if (user) {
      loadUserSettings();
    }
  }, [user]);

  // Set up real-time subscriptions and handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false);
      }
    };

    // Debug user data
    if (user) {
      console.log('User data in Home:', {
        displayName: user.displayName,
        photoURL: user.photoURL,
        email: user.email,
        uid: user.uid
      });

      // Subscribe to users table changes for real-time updates
      console.log('Setting up real-time subscription for user:', user.uid);
      const userSettingsSubscription = supabase
        .channel(`users_changes_home_${user.uid}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'users',
            filter: `id=eq.${user.uid}`
          },
          (payload) => {
            console.log('User data changed in Home:', payload);
            if (payload.new) {
              console.log('Updating Home component with new user data:', payload.new);
              setUserSettings(payload.new);
              setVaultName(payload.new.vault_name || payload.new.display_name || 'my vault');
            }
          }
        )
        .subscribe((status) => {
          console.log('Home subscription status:', status);
        });

      // Listen for manual vault settings updates for immediate response
      const handleVaultSettingsUpdate = (event: CustomEvent) => {
        console.log('Manual vault settings update received in Home:', event.detail);
        // Update vault name from the complete data
        if (event.detail.vault_name !== undefined) {
          setVaultName(event.detail.vault_name || 'my vault');
        }
      };

      window.addEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);
        userSettingsSubscription.unsubscribe();
      };
    } else {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      // Show loading immediately
      setLogoutLoading(true);

      // Add a small delay to ensure loading screen shows
      await new Promise(resolve => setTimeout(resolve, 100));

      await signOut();

      toast({
        title: "Goodbye!",
        description: "You've been signed out successfully.",
        variant: "success"
      });

      // Add a longer delay for smooth transition
      setTimeout(() => {
        setLogoutLoading(false);
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      setLogoutLoading(false);
      toast({
        title: "Logout Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const sidebarItems = [
    { icon: HomeIcon, label: "home", active: true, path: "/home" },
    { icon: Archive, label: vaultName.toLowerCase(), active: false, path: "/my-vault" },
    { icon: Brain, label: "mind games", active: false, path: "/mind-games" },
    { icon: Map, label: "visual maps", active: false, path: "/visual-maps" },
    { icon: Trophy, label: "achievements", active: false, path: "/achievements" },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const didYouKnowTips = [
    "Spaced repetition works by reviewing content at increasing intervals, improving long-term memory. Try our quiz system!",
    "Active recall is 10x more effective than passive reading for long-term retention.",
    "Taking regular quizzes can improve your long-term retention by up to 40%! Try creating a quiz from your recent notes to reinforce your learning.",
    "The human brain can store approximately 2.5 petabytes of information - that's equivalent to 3 million hours of TV shows!",
    "Mind mapping activates both hemispheres of your brain, making it easier to remember and understand complex topics.",
    "Studies show that handwriting notes (even digitally) improves comprehension compared to typing.",
  ];

  const refreshTip = () => {
    setCurrentTip((prev) => (prev + 1) % didYouKnowTips.length);
  };

  const closeTipInHero = () => {
    setShowTipInHero(false);
  };



  const addTodo = () => {
    if (newTodoText.trim()) {
      setTodos(prev => [...prev, {
        id: Date.now(),
        text: newTodoText.trim(),
        completed: false
      }]);
      setNewTodoText("");
    }
  };

  const toggleTodo = (id: number) => {
    setTodos(prev => prev.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const deleteTodo = (id: number) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
  };

  return (
    <>
      {logoutLoading && <LoadingScreen message="signing you out..." />}
      <div
        className="min-h-screen w-full font-sans relative"
        style={{
          backgroundImage: 'url(/background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        {/* Subtle overlay to tone down the background */}
        <div className="absolute inset-0 bg-[#2E0406]/40 backdrop-blur-[0.5px]" />

      {/* Top Navigation Bar */}
      <div className="w-full px-3 py-6 relative z-10">
        <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4 relative z-50 max-w-[90%] mx-auto">
          <div className="flex items-center justify-between">
            {/* Left: Hamburger/Close and Logo */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
              >
                {sidebarOpen ? (
                  <X className="w-4 h-4 text-[#5a4a3a]/80" />
                ) : (
                  <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                )}
              </Button>
              <div className="flex items-center gap-3">
                <img
                  src="/app logo.png"
                  alt="Logo"
                  className="w-9 h-9 rounded-xl object-cover shadow-sm"
                />
              </div>
            </div>

            {/* Center: Search Bar */}
            <div className="flex-1 max-w-lg mx-6">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#8b7355]/80" />
                <Input
                  placeholder="Search anything..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setSearchModalOpen(true)}
                  className="bg-white/70 backdrop-blur-sm border border-[#d4c7b8]/40 rounded-2xl h-11 pl-12 pr-4 text-[#2E0406] placeholder:text-[#8b7355]/60 font-poppins text-sm focus:ring-2 focus:ring-[#8b7355]/40 focus:border-[#8b7355]/60 focus:bg-white/80 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md"
                />
              </div>
            </div>

            {/* Right: Level, Notifications, User */}
            <div className="flex items-center gap-3">
              {/* Level Progress - More Compact */}
              <div className="hidden md:flex items-center gap-3 bg-white/50 backdrop-blur-sm rounded-2xl px-4 py-2 border border-[#d4c7b8]/30">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-poppins font-medium text-[#2E0406]/90">Lv.1</span>
                  <div className="w-16 h-1.5 bg-[#d4c7b8]/60 rounded-full overflow-hidden">
                    <div className="w-0 h-full bg-gradient-to-r from-[#8b7355] to-[#a68b5b] rounded-full transition-all duration-300" />
                  </div>
                  <span className="text-xs font-poppins text-[#8b7355]/70 font-medium">0/20</span>
                </div>
              </div>

              {/* Notifications */}
              <div className="relative" ref={notificationDropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
                  className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200"
                >
                  <Bell className="w-5 h-5 text-[#5a4a3a]/80" />
                </Button>

                {/* Notification Dropdown */}
                {notificationDropdownOpen && (
                  <div className="absolute right-0 top-12 w-64 bg-[#faf7f2]/70 backdrop-blur-xl border border-[#e0d7cc]/40 rounded-xl shadow-2xl p-4 z-[200] before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#faf7f2]/30 before:via-[#f0ebe3]/20 before:to-[#e8ddd0]/25 before:rounded-xl before:pointer-events-none">
                    <h3 className="font-poppins font-semibold text-[#2E0406] mb-2 relative z-10">Notifications</h3>
                    <p className="text-[#8b7355] text-sm relative z-10">No new notifications</p>
                  </div>
                )}
              </div>

              {/* User */}
              <div className="relative flex items-center gap-2" ref={userDropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200 p-0"
                >
                  {user?.photoURL ? (
                    <img
                      src={user.photoURL}
                      alt="Profile"
                      className="w-8 h-8 rounded-full object-cover"
                      onError={(e) => {
                        console.log('Profile image failed to load:', user.photoURL);
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  <User className={`w-5 h-5 text-[#5a4a3a]/80 ${user?.photoURL ? 'hidden' : ''}`} />
                </Button>
                <span
                  className="text-sm font-poppins text-[#2E0406]/90 cursor-pointer"
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                >
                  {user?.displayName || 'user'}
                </span>

                {/* User Dropdown */}
                {userDropdownOpen && (
                  <div className="absolute right-0 top-12 w-48 bg-[#faf7f2]/70 backdrop-blur-xl border border-[#e0d7cc]/40 rounded-xl shadow-2xl p-2 z-[200] before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#faf7f2]/30 before:via-[#f0ebe3]/20 before:to-[#e8ddd0]/25 before:rounded-xl before:pointer-events-none">
                    <div className="space-y-1 relative z-10">
                      <div className="px-3 py-2 text-[#2E0406] font-poppins font-medium text-sm border-b border-[#e0d7cc]/40">
                        {user?.displayName || 'User'}
                      </div>
                      <div className="px-3 py-1 text-[#8b7355] font-poppins text-xs">
                        {user?.email}
                      </div>
                      <button
                        className="w-full text-left px-3 py-2 text-[#5a4a3a] font-poppins text-sm hover:bg-[#e0d7cc]/30 rounded-lg transition-colors"
                        onClick={() => {
                          // Handle settings click
                          setUserDropdownOpen(false);
                        }}
                      >
                        Settings
                      </button>
                      <button
                        className="w-full text-left px-3 py-2 text-red-600 font-poppins text-sm hover:bg-red-50 rounded-lg transition-colors"
                        onClick={() => {
                          signOut();
                          setUserDropdownOpen(false);
                        }}
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
        {/* Sidebar */}
        <div className={`transition-all duration-500 ease-in-out ${
          sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
        }`}>
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
            {/* Full Sidebar Content */}
            <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
            }`}>
              {/* Navigation Items */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                      item.active
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-r before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                    <span className="relative z-10">{item.label}</span>
                  </Button>
                ))}
              </div>

              {/* Logout Button */}
              <div className="absolute bottom-6 left-6 right-6">
                <Button
                  onClick={handleLogout}
                  variant="ghost"
                  className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md transition-all duration-800 relative"
                >
                  <LogOut className="w-4 h-4 relative z-10" />
                  <span className="relative z-10">logout</span>
                </Button>
              </div>
            </div>

            {/* Icon-Only Sidebar Content */}
            <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
            }`}>
              {/* Navigation Icons */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="icon"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-10 h-10 rounded-xl transition-all duration-200 relative ${
                      item.active
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                    title={item.label}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                  </Button>
                ))}
              </div>

              {/* Logout Icon */}
              <div className="absolute bottom-3 left-3 right-3">
                <Button
                  onClick={handleLogout}
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 rounded-xl text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md transition-all duration-200 relative"
                  title="logout"
                >
                  <LogOut className="w-4 h-4 relative z-10" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 transition-all duration-500 ease-in-out">
          <div className="bg-gradient-to-br from-[#f8f4ee] via-[#faf7f2] to-[#f5f0e8] h-full overflow-hidden">
            {/* Scrollable Content Container */}
            <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
              <div className="p-8">
                {/* Hero Section with Stats */}
                <div className="relative bg-gradient-to-br from-white/95 via-white/90 to-white/85 backdrop-blur-md border border-white/40 rounded-3xl p-8 mb-8 overflow-hidden shadow-2xl">
                  {/* Roses Background */}
                  <div
                    className="absolute inset-0 rounded-3xl opacity-100"
                    style={{
                      backgroundImage: 'url(/roses-bg.jpg)',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat'
                    }}
                  ></div>
                  {/* Dark overlay for better text readability */}
                  <div className="absolute inset-0 bg-black/20 rounded-3xl"></div>
                  {/* Decorative Elements */}
                  <div className="absolute top-4 right-4 w-24 h-24 bg-gradient-to-br from-[#8b2635]/20 to-[#6d1f2c]/20 rounded-full blur-xl"></div>
                  <div className="absolute bottom-4 left-4 w-32 h-32 bg-gradient-to-br from-[#8b2635]/10 to-[#6d1f2c]/10 rounded-full blur-xl"></div>

                  {/* Did You Know Tip - Top Right Corner */}
                  {showTipInHero && (
                    <div className="absolute top-6 right-6 max-w-sm bg-white/90 backdrop-blur-md border border-white/50 rounded-2xl p-4 z-30 shadow-lg">
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-6 h-6 bg-gradient-to-br from-[#8b2635] to-[#6d1f2c] rounded-lg flex items-center justify-center">
                              <HelpCircle className="w-3 h-3 text-white" />
                            </div>
                            <span className="text-[#8b2635] font-poppins text-xs uppercase tracking-wider font-semibold">Tip</span>
                          </div>
                          <p className="text-[#2E0406] font-poppins text-xs leading-relaxed font-medium">
                            {didYouKnowTips[currentTip]}
                          </p>
                        </div>
                        <div className="flex gap-1 flex-shrink-0">
                          <button
                            onClick={refreshTip}
                            className="w-6 h-6 bg-[#8b2635]/10 hover:bg-[#8b2635]/20 rounded-lg flex items-center justify-center transition-all duration-200 group"
                            title="New tip"
                          >
                            <RefreshCw className="w-3 h-3 text-[#8b2635] group-hover:rotate-180 transition-transform duration-300" />
                          </button>
                          <button
                            onClick={closeTipInHero}
                            className="w-6 h-6 bg-[#8b2635]/10 hover:bg-[#8b2635]/20 rounded-lg flex items-center justify-center transition-all duration-200"
                            title="Close tip"
                          >
                            <X className="w-3 h-3 text-[#8b2635]" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="relative z-20">
                    {/* Welcome Message */}
                    <div className="mb-8">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-2 h-2 bg-white rounded-full shadow-sm"></div>
                        <span className="text-white font-poppins text-sm uppercase tracking-wider font-semibold drop-shadow-sm">Knowledge Sanctuary</span>
                      </div>
                      <h1 className="text-4xl md:text-5xl font-cormorant font-bold text-white mb-3 drop-shadow-md">
                        Welcome back,<br />
                        <span className="text-white/90">{user?.displayName || 'Explorer'}</span>
                      </h1>
                      <p className="text-white/90 font-poppins text-lg max-w-md drop-shadow-sm">
                        Ready to expand your intellectual horizons today?
                      </p>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-4 hover:bg-white/25 transition-all duration-300 shadow-lg">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                            <img src="/xp-icon.png" alt="XP Points" className="w-12 h-12" />
                          </div>
                          <p className="text-2xl font-bold text-white mb-1 drop-shadow-sm">0</p>
                          <p className="text-white/90 font-poppins text-xs font-medium">XP Points</p>
                        </div>
                      </div>

                      <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-4 hover:bg-white/25 transition-all duration-300 shadow-lg">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                            <img src="/lvl-icon.png" alt="Level" className="w-12 h-12" />
                          </div>
                          <p className="text-2xl font-bold text-white mb-1 drop-shadow-sm">1</p>
                          <p className="text-white/90 font-poppins text-xs font-medium">Level</p>
                        </div>
                      </div>

                      <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-4 hover:bg-white/25 transition-all duration-300 shadow-lg">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                            <img src="/accuracy-icon.png" alt="Accuracy" className="w-10 h-10" />
                          </div>
                          <p className="text-2xl font-bold text-white mb-1 drop-shadow-sm">0%</p>
                          <p className="text-white/90 font-poppins text-xs font-medium">Accuracy</p>
                        </div>
                      </div>

                      <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-4 hover:bg-white/25 transition-all duration-300 shadow-lg">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                            <img src="/notes-icon.png" alt="Notes" className="w-12 h-12" />
                          </div>
                          <p className="text-2xl font-bold text-white mb-1 drop-shadow-sm">0</p>
                          <p className="text-white/90 font-poppins text-xs font-medium">Notes</p>
                        </div>
                      </div>

                      <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-4 hover:bg-white/25 transition-all duration-300 shadow-lg">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 shadow-md">
                            <img src="/streak-icon.png" alt="Day Streak" className="w-10 h-10" />
                          </div>
                          <p className="text-2xl font-bold text-white mb-1 drop-shadow-sm">0</p>
                          <p className="text-white/90 font-poppins text-xs font-medium">Day Streak</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions & To-Do/Goals Side by Side */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                  {/* Quick Access Section */}
                  <div className="bg-gradient-to-br from-white/85 via-white/80 to-white/75 backdrop-blur-md border border-white/30 rounded-3xl p-8 shadow-xl">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-[#8b2635] rounded-full"></div>
                          <span className="text-[#8b2635] font-poppins text-sm uppercase tracking-wider">Quick Actions</span>
                        </div>
                        <h2 className="text-2xl font-cormorant font-bold text-[#2E0406]">Start Your Journey</h2>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {/* Create New Block */}
                      <button
                        onClick={() => navigate('/my-vault')}
                        className="group relative bg-gradient-to-br from-[#8b2635]/10 to-[#6d1f2c]/10 hover:from-[#8b2635]/15 hover:to-[#6d1f2c]/15 border border-[#8b2635]/20 rounded-2xl p-5 transition-all duration-300 hover:scale-105 hover:shadow-lg text-left"
                      >
                        <div className="absolute top-3 right-3 w-6 h-6 bg-[#8b2635]/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="w-12 h-12 bg-gradient-to-br from-[#8b2635] to-[#6d1f2c] rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                          <Plus className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-poppins font-semibold text-[#2E0406] mb-1 text-sm">Create Block</h3>
                        <p className="text-xs text-[#6d1f2c] leading-relaxed">Start a new topic</p>
                      </button>

                      {/* Quick Note */}
                      <button className="group relative bg-gradient-to-br from-[#7a4b8c]/10 to-[#5d3a6b]/10 hover:from-[#7a4b8c]/15 hover:to-[#5d3a6b]/15 border border-[#7a4b8c]/20 rounded-2xl p-5 transition-all duration-300 hover:scale-105 hover:shadow-lg text-left">
                        <div className="absolute top-3 right-3 w-6 h-6 bg-[#7a4b8c]/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="w-12 h-12 bg-gradient-to-br from-[#7a4b8c] to-[#5d3a6b] rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                          <FileText className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-poppins font-semibold text-[#2E0406] mb-1 text-sm">Quick Note</h3>
                        <p className="text-xs text-[#6d1f2c] leading-relaxed">Capture thoughts</p>
                      </button>

                      {/* Take Quiz */}
                      <button className="group relative bg-gradient-to-br from-[#6b4423]/10 to-[#5a3a1e]/10 hover:from-[#6b4423]/15 hover:to-[#5a3a1e]/15 border border-[#6b4423]/20 rounded-2xl p-5 transition-all duration-300 hover:scale-105 hover:shadow-lg text-left">
                        <div className="absolute top-3 right-3 w-6 h-6 bg-[#6b4423]/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="w-12 h-12 bg-gradient-to-br from-[#6b4423] to-[#5a3a1e] rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                          <HelpCircle className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-poppins font-semibold text-[#2E0406] mb-1 text-sm">Take Quiz</h3>
                        <p className="text-xs text-[#6d1f2c] leading-relaxed">Test knowledge</p>
                      </button>

                      {/* Mind Map */}
                      <button className="group relative bg-gradient-to-br from-[#8b5a2b]/10 to-[#6d4522]/10 hover:from-[#8b5a2b]/15 hover:to-[#6d4522]/15 border border-[#8b5a2b]/20 rounded-2xl p-5 transition-all duration-300 hover:scale-105 hover:shadow-lg text-left">
                        <div className="absolute top-3 right-3 w-6 h-6 bg-[#8b5a2b]/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="w-12 h-12 bg-gradient-to-br from-[#8b5a2b] to-[#6d4522] rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                          <Map className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-poppins font-semibold text-[#2E0406] mb-1 text-sm">Mind Map</h3>
                        <p className="text-xs text-[#6d1f2c] leading-relaxed">Visualize ideas</p>
                      </button>
                    </div>
                  </div>

                  {/* To-Do/Goals Section */}
                  <div className="bg-gradient-to-br from-white/60 to-white/40 backdrop-blur-sm border border-[#8b2635]/20 rounded-3xl p-8 shadow-lg">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-[#8b2635] rounded-full"></div>
                          <span className="text-[#8b2635] font-poppins text-sm uppercase tracking-wider">Goals</span>
                        </div>
                        <h2 className="text-2xl font-cormorant font-bold text-[#2E0406]">Learning Goals</h2>
                      </div>
                    </div>

                    {/* Add New Task */}
                    <div className="mb-6">
                      <div className="flex gap-3">
                        <input
                          type="text"
                          value={newTodoText}
                          onChange={(e) => setNewTodoText(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && addTodo()}
                          placeholder="Add a new learning goal..."
                          className="flex-1 bg-white/60 border border-[#8b2635]/20 rounded-xl px-4 py-3 font-poppins text-sm text-[#2E0406] placeholder-[#6d1f2c]/60 focus:outline-none focus:ring-2 focus:ring-[#8b2635]/20 focus:border-[#8b2635] transition-all duration-200"
                        />
                        <button
                          onClick={addTodo}
                          className="bg-gradient-to-br from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#5a1a24] text-white px-6 py-3 rounded-xl font-poppins font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center gap-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add
                        </button>
                      </div>
                    </div>

                    {/* Todo List */}
                    <div className="space-y-3 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
                      {todos.length === 0 ? (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-[#e0d7cc]/40 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <Plus className="w-8 h-8 text-[#8b7355]" />
                          </div>
                          <h3 className="font-poppins font-semibold text-[#2E0406] mb-2">No goals yet</h3>
                          <p className="text-sm text-[#8b7355]">Add your first learning goal above</p>
                        </div>
                      ) : (
                        todos.map((todo) => (
                          <div
                            key={todo.id}
                            className={`group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-xl p-4 hover:bg-white/80 transition-all duration-200 ${
                              todo.completed ? 'opacity-60' : ''
                            }`}
                          >
                            <div className="flex items-center gap-3">
                              <button
                                onClick={() => toggleTodo(todo.id)}
                                className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
                                  todo.completed
                                    ? 'bg-green-500 border-green-500 text-white'
                                    : 'border-[#d4c7b8] hover:border-green-400 hover:bg-green-50'
                                }`}
                              >
                                {todo.completed && <Check className="w-4 h-4" />}
                              </button>
                              <span
                                className={`flex-1 font-poppins text-sm transition-all duration-200 ${
                                  todo.completed
                                    ? 'text-[#8b7355] line-through'
                                    : 'text-[#2E0406]'
                                }`}
                              >
                                {todo.text}
                              </span>
                              <button
                                onClick={() => deleteTodo(todo.id)}
                                className="opacity-0 group-hover:opacity-100 w-8 h-8 bg-red-100 hover:bg-red-200 text-red-600 rounded-lg flex items-center justify-center transition-all duration-200"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* Progress Summary */}
                    {todos.length > 0 && (
                      <div className="mt-6 pt-6 border-t border-[#e0d7cc]/40">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-poppins text-[#8b7355]">
                            {todos.filter(t => t.completed).length} of {todos.length} completed
                          </span>
                          <div className="flex items-center gap-2">
                            <div className="w-20 h-2 bg-[#e0d7cc]/40 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-gradient-to-r from-green-400 to-green-500 transition-all duration-500"
                                style={{
                                  width: `${todos.length > 0 ? (todos.filter(t => t.completed).length / todos.length) * 100 : 0}%`
                                }}
                              ></div>
                            </div>
                            <span className="font-poppins font-medium text-[#2E0406]">
                              {todos.length > 0 ? Math.round((todos.filter(t => t.completed).length / todos.length) * 100) : 0}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Recently Viewed Section */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                  {/* Main Featured Item */}
                  <div className="lg:col-span-2 bg-gradient-to-br from-white/85 via-white/80 to-white/75 backdrop-blur-md border border-white/30 rounded-3xl p-8 relative overflow-hidden shadow-xl">
                    <div className="absolute top-6 right-6 flex gap-2">
                      <div className="w-3 h-3 bg-[#8b2635] rounded-full"></div>
                      <div className="w-3 h-3 bg-[#7a4b8c] rounded-full"></div>
                      <div className="w-3 h-3 bg-[#6b4423] rounded-full"></div>
                    </div>

                    <div className="flex items-start justify-between mb-6">
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-2 h-2 bg-[#8b2635] rounded-full"></div>
                          <span className="text-[#8b2635] font-poppins text-sm uppercase tracking-wider">Continue Learning</span>
                        </div>
                        <h2 className="text-3xl md:text-4xl font-cormorant font-bold text-[#2E0406] mb-3">
                          Egyptian Pharaohs<br />
                          <span className="text-[#8b2635]">Ancient Dynasties</span>
                        </h2>
                        <div className="flex items-center gap-4 mb-6">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-[#8b2635] rounded-lg flex items-center justify-center">
                              <span className="text-white font-bold text-sm">01</span>
                            </div>
                            <div>
                              <p className="font-poppins font-semibold text-[#2E0406] text-sm">Deep Dive</p>
                              <p className="text-[#6d1f2c] text-xs">Explore the mysteries of ancient rulers</p>
                            </div>
                          </div>
                        </div>
                        <button className="bg-[#8b2635] hover:bg-[#6d1f2c] text-white px-6 py-3 rounded-xl font-poppins font-medium transition-all duration-300 hover:scale-105 flex items-center gap-2">
                          Continue Reading
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Decorative Element */}
                    <div className="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-br from-[#8b2635]/20 to-[#6d1f2c]/20 rounded-full blur-3xl"></div>
                    <div className="absolute top-1/2 right-8 w-32 h-32 bg-[#8b2635]/5 rounded-2xl transform rotate-12"></div>
                  </div>

                  {/* Side Items */}
                  <div className="space-y-6">
                    {/* Quick Access Item */}
                    <div className="bg-white/70 backdrop-blur-sm border border-[#7a4b8c]/30 rounded-2xl p-6 hover:bg-white/80 transition-all duration-300 cursor-pointer">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-2 h-2 bg-[#7a4b8c] rounded-full"></div>
                        <span className="text-[#7a4b8c] font-poppins text-xs uppercase tracking-wider">Recent Study</span>
                      </div>
                      <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Quantum Mechanics</h3>
                      <p className="text-[#6d1f2c] font-poppins text-sm mb-4">Understanding wave-particle duality</p>
                      <div className="flex items-center justify-between">
                        <span className="text-[#6d1f2c] text-xs">Physics • 1 day ago</span>
                        <div className="w-8 h-8 bg-[#7a4b8c]/10 rounded-lg flex items-center justify-center">
                          <Brain className="w-4 h-4 text-[#7a4b8c]" />
                        </div>
                      </div>
                    </div>

                    {/* Another Item */}
                    <div className="bg-white/70 backdrop-blur-sm border border-[#6b4423]/30 rounded-2xl p-6 hover:bg-white/80 transition-all duration-300 cursor-pointer">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-2 h-2 bg-[#6b4423] rounded-full"></div>
                        <span className="text-[#6b4423] font-poppins text-xs uppercase tracking-wider">Art History</span>
                      </div>
                      <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Renaissance Art</h3>
                      <p className="text-[#6d1f2c] font-poppins text-sm mb-4">Masters of the Italian Renaissance</p>
                      <div className="flex items-center justify-between">
                        <span className="text-[#6d1f2c] text-xs">Art • 3 days ago</span>
                        <div className="w-8 h-8 bg-[#6b4423]/10 rounded-lg flex items-center justify-center">
                          <Archive className="w-4 h-4 text-[#6b4423]" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Achievements Section */}
                <div className="bg-gradient-to-br from-white/80 via-white/75 to-white/70 backdrop-blur-md border border-white/30 rounded-3xl p-8 mb-8 shadow-xl">
                  <div className="flex items-center justify-between mb-8">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-2 h-2 bg-[#8b2635] rounded-full"></div>
                        <span className="text-[#8b2635] font-poppins text-sm uppercase tracking-wider">Achievements</span>
                      </div>
                      <h2 className="text-2xl font-cormorant font-bold text-[#2E0406]">Your Progress</h2>
                    </div>
                    <button className="text-[#8b2635] hover:text-[#6d1f2c] font-poppins text-sm transition-colors flex items-center gap-2">
                      View All
                      <div className="w-6 h-6 bg-[#8b2635]/10 rounded-full flex items-center justify-center">
                        <span className="text-xs">→</span>
                      </div>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Knowledge Seeker */}
                    <div className="group bg-gradient-to-br from-[#8b2635]/10 to-[#6d1f2c]/10 border border-[#8b2635]/20 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="relative mb-4">
                          <div className="w-20 h-20 bg-gradient-to-br from-[#8b2635] to-[#6d1f2c] rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <Award className="w-10 h-10 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                            <span className="text-xs font-bold text-[#8b2635]">0</span>
                          </div>
                        </div>
                        <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Knowledge Seeker</h3>
                        <p className="text-sm text-[#6d1f2c] mb-4">Create your first 10 knowledge blocks</p>
                        <div className="bg-white/60 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] h-3 rounded-full transition-all duration-500" style={{ width: '0%' }}></div>
                        </div>
                        <p className="text-xs text-[#6d1f2c] font-medium">0 / 10 blocks</p>
                      </div>
                    </div>

                    {/* Quiz Master */}
                    <div className="group bg-gradient-to-br from-[#7a4b8c]/10 to-[#5d3a6b]/10 border border-[#7a4b8c]/20 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="relative mb-4">
                          <div className="w-20 h-20 bg-gradient-to-br from-[#7a4b8c] to-[#5d3a6b] rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <Trophy className="w-10 h-10 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                            <span className="text-xs font-bold text-[#7a4b8c]">0</span>
                          </div>
                        </div>
                        <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Quiz Master</h3>
                        <p className="text-sm text-[#6d1f2c] mb-4">Achieve 95% quiz accuracy</p>
                        <div className="bg-white/60 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-[#7a4b8c] to-[#5d3a6b] h-3 rounded-full transition-all duration-500" style={{ width: '0%' }}></div>
                        </div>
                        <p className="text-xs text-[#6d1f2c] font-medium">0% accuracy</p>
                      </div>
                    </div>

                    {/* Streak Keeper */}
                    <div className="group bg-gradient-to-br from-[#6b4423]/10 to-[#5a3a1e]/10 border border-[#6b4423]/20 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="relative mb-4">
                          <div className="w-20 h-20 bg-gradient-to-br from-[#6b4423] to-[#5a3a1e] rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <TrendingUp className="w-10 h-10 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                            <span className="text-xs font-bold text-[#6b4423]">0</span>
                          </div>
                        </div>
                        <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Streak Keeper</h3>
                        <p className="text-sm text-[#6d1f2c] mb-4">Maintain a 7-day study streak</p>
                        <div className="bg-white/60 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-[#6b4423] to-[#5a3a1e] h-3 rounded-full transition-all duration-500" style={{ width: '0%' }}></div>
                        </div>
                        <p className="text-xs text-[#6d1f2c] font-medium">0 / 7 days</p>
                      </div>
                    </div>

                    {/* Knowledge Vault */}
                    <div className="group bg-gradient-to-br from-[#8b5a2b]/10 to-[#6d4522]/10 border border-[#8b5a2b]/20 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                      <div className="text-center">
                        <div className="relative mb-4">
                          <div className="w-20 h-20 bg-gradient-to-br from-[#8b5a2b] to-[#6d4522] rounded-full flex items-center justify-center mx-auto shadow-lg">
                            <Archive className="w-10 h-10 text-white" />
                          </div>
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                            <span className="text-xs font-bold text-[#8b5a2b]">0</span>
                          </div>
                        </div>
                        <h3 className="font-cormorant font-bold text-[#2E0406] text-lg mb-2">Knowledge Vault</h3>
                        <p className="text-sm text-[#6d1f2c] mb-4">Publish 100 notes to share</p>
                        <div className="bg-white/60 rounded-full h-3 mb-2">
                          <div className="bg-gradient-to-r from-[#8b5a2b] to-[#6d4522] h-3 rounded-full transition-all duration-500" style={{ width: '0%' }}></div>
                        </div>
                        <p className="text-xs text-[#6d1f2c] font-medium">0 / 100 notes</p>
                      </div>
                    </div>
                  </div>
                </div>



                {/* Bottom Spacing */}
                <div className="h-8"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Modal */}
      {searchModalOpen && (
        <div
          className="fixed inset-0 bg-[#1e0202]/40 backdrop-blur-sm z-[200] flex items-start justify-center pt-20"
          onClick={() => setSearchModalOpen(false)}
        >
          <div
            className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-2xl mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-poppins font-semibold text-[#2E0406]">Search</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchModalOpen(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>

              <div className="relative mb-6">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#8b2635]/80" />
                <Input
                  placeholder="Search for anything..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  autoFocus
                  className="bg-white/60 backdrop-blur-sm border border-[#8b2635]/20 rounded-xl h-12 pl-12 pr-4 text-[#2E0406] placeholder:text-[#6d1f2c]/70 font-poppins text-base focus:ring-2 focus:ring-[#8b2635]/60 focus:border-[#8b2635]/80 focus:bg-white/80 transition-all duration-200"
                />
              </div>

              <div className="text-center py-12">
                <Search className="w-12 h-12 text-[#8b2635]/40 mx-auto mb-4" />
                <h3 className="text-[#2E0406] font-poppins font-medium mb-2">No results found</h3>
                <p className="text-[#6d1f2c] text-sm">Try adjusting your search terms</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );
};
