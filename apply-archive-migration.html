<!DOCTYPE html>
<html>
<head>
    <title>Apply Archive Migration</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Apply Archive Content Type Migration</h1>
    <button onclick="applyMigration()">Apply Migration</button>
    <div id="output"></div>

    <script>
        const supabaseUrl = 'https://zyijbgtqthkbbtprkkod.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5aWpiZ3RxdGhrYmJ0cHJra29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Mzg1MTAsImV4cCI6MjA2OTAxNDUxMH0.jHpQv8AKlBcsl3B85et6mc4R5U6Z-1co-NK6eDc8upg';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);

        async function applyMigration() {
            const migrationSQL = `
-- Drop the existing constraint
ALTER TABLE block_contents DROP CONSTRAINT IF EXISTS block_contents_content_type_check;

-- Add the new constraint with 'archive' included
ALTER TABLE block_contents ADD CONSTRAINT block_contents_content_type_check 
CHECK (
  content_type = ANY (
    ARRAY[
      'text'::text,
      'image'::text,
      'video'::text,
      'audio'::text,
      'file'::text,
      'archive'::text
    ]
  )
);
            `;

            try {
                const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
                document.getElementById('output').innerHTML = `<pre>Migration Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }
    </script>
</body>
</html>
