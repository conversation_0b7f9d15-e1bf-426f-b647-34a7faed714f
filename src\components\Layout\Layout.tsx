import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "../ui/button";
import { useAuth } from "../../hooks/useAuth";
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  HelpCircle
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps): JSX.Element => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);

  const sidebarItems = [
    { icon: HomeIcon, label: "home", path: "/home" },
    { icon: Archive, label: "my vault", path: "/my-vault" },
    { icon: Brain, label: "mind games", path: "/mind-games" },
    { icon: Map, label: "visual maps", path: "/visual-maps" },
    { icon: Trophy, label: "achievements", path: "/achievements" },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setLogoutLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#faf7f2] to-[#f5f1ea] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Header */}
      <div className="relative z-20 bg-white/80 backdrop-blur-md border-b border-white/30 shadow-sm">
        <div className="flex items-center justify-between px-6 py-4">
          {/* Left side - Logo and hamburger */}
          <div className="flex items-center gap-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="w-10 h-10 bg-[#8b2635]/10 hover:bg-[#8b2635]/20 rounded-xl flex items-center justify-center transition-all duration-200 group"
            >
              {sidebarOpen ? (
                <X className="w-5 h-5 text-[#8b2635] group-hover:scale-110 transition-transform" />
              ) : (
                <Menu className="w-5 h-5 text-[#8b2635] group-hover:scale-110 transition-transform" />
              )}
            </button>
            
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-[#8b2635] to-[#6d1f2c] rounded-lg flex items-center justify-center shadow-md">
                <span className="text-white font-bold text-sm">K</span>
              </div>
              <span className="font-cormorant font-bold text-[#2E0406] text-xl">Kairo</span>
            </div>
          </div>

          {/* Right side - Search, notifications, user */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSearchModalOpen(true)}
              className="w-10 h-10 rounded-xl hover:bg-[#8b2635]/10 transition-all duration-200"
            >
              <Search className="w-5 h-5 text-[#8b2635]" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
              className="w-10 h-10 rounded-xl hover:bg-[#8b2635]/10 transition-all duration-200 relative"
            >
              <Bell className="w-5 h-5 text-[#8b2635]" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#8b2635] rounded-full"></div>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setUserDropdownOpen(!userDropdownOpen)}
              className="w-10 h-10 rounded-xl hover:bg-[#8b2635]/10 transition-all duration-200 relative"
            >
              <User className="w-5 h-5 text-[#8b2635]" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
        {/* Sidebar */}
        <div className={`transition-all duration-500 ease-in-out ${
          sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
        }`}>
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
            {/* Full Sidebar Content */}
            <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
            }`}>
              {/* Navigation Items */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                      location.pathname === item.path
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-r before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                    <span className="relative z-10">{item.label}</span>
                  </Button>
                ))}
              </div>

              {/* Help Icon */}
              <div className="absolute bottom-16 left-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 rounded-xl text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] transition-all duration-200"
                  title="Help"
                >
                  <HelpCircle className="w-4 h-4" />
                </Button>
              </div>

              {/* Logout Button */}
              <div className="absolute bottom-4 left-4 right-4">
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  disabled={logoutLoading}
                  className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                >
                  <LogOut className="w-4 h-4" />
                  <span>{logoutLoading ? "Signing out..." : "Logout"}</span>
                </Button>
              </div>
            </div>

            {/* Icon-Only Sidebar Content */}
            <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
            }`}>
              {/* Navigation Icons */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="icon"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-10 h-10 rounded-xl transition-all duration-200 relative ${
                      location.pathname === item.path
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                    title={item.label}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                  </Button>
                ))}
              </div>

              {/* Help Icon */}
              <div className="absolute bottom-16 left-3">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 rounded-xl text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] transition-all duration-200"
                  title="Help"
                >
                  <HelpCircle className="w-4 h-4" />
                </Button>
              </div>

              {/* Logout Icon */}
              <div className="absolute bottom-3 left-3">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleLogout}
                  disabled={logoutLoading}
                  className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white/60 backdrop-blur-md border border-white/30 rounded-2xl shadow-lg overflow-hidden">
          {children}
        </div>
      </div>

      {/* Search Modal */}
      {searchModalOpen && (
        <div
          className="fixed inset-0 bg-[#1e0202]/40 backdrop-blur-sm z-[200] flex items-start justify-center pt-20"
          onClick={() => setSearchModalOpen(false)}
        >
          <div
            className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-2xl mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-poppins font-semibold text-[#2E0406]">Search</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchModalOpen(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#8b7355]" />
                <input
                  type="text"
                  placeholder="Search your knowledge..."
                  className="w-full pl-10 pr-4 py-3 bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder:text-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                  autoFocus
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
