import React from "react";

interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = "loading..." 
}) => {
  return (
    <div 
      className="fixed inset-0 z-[10000] flex items-center justify-center"
      style={{
        backgroundImage: 'url(/background.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed',
        filter: 'blur(1px)'
      }}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-[#2E0406]/60" />
      
      {/* Loading content */}
      <div className="relative z-10 flex flex-col items-center">
        {/* Spinning wheel */}
        <div className="relative mb-6">
          {/* Outer ring */}
          <div className="w-20 h-20 border-4 border-[#faf7f2]/20 rounded-full"></div>

          {/* Main spinning ring */}
          <div className="absolute inset-0 w-20 h-20 border-4 border-transparent border-t-[#faf7f2] border-r-[#faf7f2]/60 rounded-full animate-spin"></div>

          {/* Secondary spinning ring (slower) */}
          <div className="absolute inset-1 w-18 h-18 border-3 border-transparent border-b-[#f0ebe3]/80 rounded-full animate-spin" style={{ animationDuration: '2s', animationDirection: 'reverse' }}></div>

          {/* Inner glow */}
          <div className="absolute inset-3 w-14 h-14 border-2 border-[#f0ebe3]/40 rounded-full animate-pulse"></div>

          {/* Center dot */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-2 h-2 bg-[#faf7f2]/80 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        {/* Loading text */}
        <div className="text-center">
          <p className="text-[#faf7f2] font-poppins text-lg font-medium tracking-wide">
            {message}
          </p>
          
          {/* Animated dots */}
          <div className="flex justify-center mt-3 space-x-2">
            <div className="w-2.5 h-2.5 bg-[#faf7f2]/70 rounded-full animate-bounce"></div>
            <div className="w-2.5 h-2.5 bg-[#faf7f2]/70 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2.5 h-2.5 bg-[#faf7f2]/70 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2.5 h-2.5 bg-[#faf7f2]/70 rounded-full animate-bounce" style={{ animationDelay: '0.3s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};
