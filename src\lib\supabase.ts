import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export type User = {
  id: string;
  auth_provider: 'email' | 'google' | 'facebook';
  email: string;
  password_hash?: string;
  display_name?: string;
  username?: string;
  profile_pic_url?: string;
  bio?: string;
  xp: number;
  level: number;
  theme: string;
  google_id?: string;
  facebook_id?: string;
  created_at: string;
  updated_at: string;
};