-- Create tag_detection_rules table for smart tag detection
CREATE TABLE IF NOT EXISTS tag_detection_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tag_name VARCHAR(100) NOT NULL,
  keywords TEXT[] NOT NULL,
  priority INTEGER NOT NULL DEFAULT 1,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_active ON tag_detection_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_priority ON tag_detection_rules(priority DESC);
CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_tag_name ON tag_detection_rules(tag_name);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON>reate trigger to automatically update updated_at
CREATE TRIGGER update_tag_detection_rules_updated_at 
    BEFORE UPDATE ON tag_detection_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add some comments for documentation
COMMENT ON TABLE tag_detection_rules IS 'Rules for automatically detecting appropriate tags based on block names';
COMMENT ON COLUMN tag_detection_rules.tag_name IS 'The tag that will be assigned when keywords match';
COMMENT ON COLUMN tag_detection_rules.keywords IS 'Array of keywords to match against block names';
COMMENT ON COLUMN tag_detection_rules.priority IS 'Higher priority rules are checked first (higher number = higher priority)';
COMMENT ON COLUMN tag_detection_rules.is_active IS 'Whether this rule is currently active and should be used for detection';
