-- Populate tag detection rules with comprehensive keywords
-- Run this in your Supabase SQL Editor after creating the tag_detection_rules table

-- Clear existing rules (optional - remove this line if you want to keep existing rules)
-- DELETE FROM tag_detection_rules;

-- Insert comprehensive tag detection rules
INSERT INTO tag_detection_rules (tag_name, keywords, priority, is_active) VALUES
(
  'Health & Medicine',
  ARRAY[
    'medicine', 'medical', 'health', 'doctor', 'hospital', 'pharmacy',
    'drug', 'medication', 'treatment', 'therapy', 'diagnosis', 'symptom',
    'disease', 'illness', 'wellness', 'fitness', 'nutrition', 'diet',
    'exercise', 'mental health', 'psychology', 'psychiatry', 'surgery',
    'clinic', 'nursing', 'anatomy', 'physiology', 'pathology', 'radiology',
    'cardiology', 'neurology', 'oncology', 'pediatric', 'geriatric',
    'dental', 'ophthalmology', 'dermatology', 'orthopedic', 'vaccine',
    'immunology', 'epidemiology', 'pharmacology', 'healthcare', 'patient'
  ],
  10,
  true
),
(
  'Technology',
  ARRAY[
    'technology', 'tech', 'computer', 'software', 'hardware', 'programming',
    'coding', 'development', 'app', 'application', 'website', 'web',
    'internet', 'digital', 'online', 'cyber', 'data', 'database',
    'algorithm', 'artificial intelligence', 'ai', 'machine learning',
    'blockchain', 'cryptocurrency', 'cloud', 'server', 'network',
    'security', 'cybersecurity', 'mobile', 'smartphone', 'tablet',
    'laptop', 'desktop', 'gaming', 'virtual reality', 'vr', 'ar'
  ],
  9,
  true
),
(
  'Education',
  ARRAY[
    'education', 'learning', 'study', 'school', 'university', 'college',
    'course', 'class', 'lesson', 'tutorial', 'training', 'teaching',
    'student', 'teacher', 'professor', 'academic', 'research',
    'knowledge', 'skill', 'certification', 'degree', 'diploma',
    'exam', 'test', 'quiz', 'homework', 'assignment', 'project',
    'thesis', 'dissertation', 'scholarship', 'curriculum', 'syllabus'
  ],
  8,
  true
),
(
  'Business',
  ARRAY[
    'business', 'company', 'corporate', 'enterprise', 'startup',
    'entrepreneur', 'management', 'marketing', 'sales', 'finance',
    'accounting', 'investment', 'profit', 'revenue', 'budget',
    'strategy', 'planning', 'meeting', 'presentation', 'project',
    'team', 'leadership', 'hr', 'human resources', 'recruitment',
    'client', 'customer', 'service', 'product', 'brand', 'market'
  ],
  7,
  true
),
(
  'Art & Creativity',
  ARRAY[
    'art', 'creative', 'creativity', 'design', 'drawing', 'painting',
    'sculpture', 'photography', 'music', 'singing', 'dancing',
    'writing', 'poetry', 'literature', 'novel', 'story', 'theater',
    'drama', 'film', 'movie', 'cinema', 'animation', 'illustration',
    'graphic design', 'fashion', 'craft', 'handmade', 'diy',
    'artistic', 'aesthetic', 'beautiful', 'inspiration', 'imagination'
  ],
  6,
  true
),
(
  'Travel',
  ARRAY[
    'travel', 'trip', 'vacation', 'holiday', 'journey', 'adventure',
    'destination', 'flight', 'hotel', 'accommodation', 'booking',
    'tourism', 'tourist', 'sightseeing', 'explore', 'discovery',
    'country', 'city', 'culture', 'local', 'international',
    'passport', 'visa', 'luggage', 'backpack', 'itinerary',
    'guide', 'map', 'restaurant', 'food', 'cuisine', 'beach'
  ],
  5,
  true
),
(
  'Food & Cooking',
  ARRAY[
    'food', 'cooking', 'recipe', 'kitchen', 'chef', 'restaurant',
    'meal', 'breakfast', 'lunch', 'dinner', 'snack', 'ingredient',
    'spice', 'flavor', 'taste', 'delicious', 'cuisine', 'dish',
    'baking', 'grilling', 'frying', 'boiling', 'healthy eating',
    'nutrition', 'diet', 'vegetarian', 'vegan', 'organic',
    'fresh', 'homemade', 'gourmet', 'culinary', 'beverage', 'drink'
  ],
  4,
  true
),
(
  'Personal',
  ARRAY[
    'personal', 'diary', 'journal', 'thoughts', 'reflection',
    'goals', 'dreams', 'aspirations', 'memories', 'experiences',
    'feelings', 'emotions', 'relationships', 'family', 'friends',
    'hobby', 'interest', 'passion', 'lifestyle', 'routine',
    'habit', 'self-improvement', 'mindfulness', 'meditation',
    'gratitude', 'achievement', 'milestone', 'celebration'
  ],
  2,
  true
),
(
  'Science & Research',
  ARRAY[
    'science', 'research', 'experiment', 'hypothesis', 'theory',
    'data', 'analysis', 'statistics', 'mathematics', 'physics',
    'chemistry', 'biology', 'astronomy', 'geology', 'ecology',
    'environment', 'climate', 'nature', 'wildlife', 'conservation',
    'laboratory', 'microscope', 'telescope', 'formula', 'equation',
    'discovery', 'innovation', 'scientific', 'study', 'observation',
    'conclusion', 'peer review', 'publication', 'journal', 'conference'
  ],
  8,
  true
),
(
  'Sports & Fitness',
  ARRAY[
    'sports', 'fitness', 'exercise', 'workout', 'gym', 'training',
    'athlete', 'competition', 'tournament', 'championship', 'team',
    'coach', 'player', 'game', 'match', 'score', 'win', 'lose',
    'football', 'basketball', 'soccer', 'tennis', 'golf', 'swimming',
    'running', 'cycling', 'hiking', 'climbing', 'skiing', 'surfing',
    'yoga', 'pilates', 'crossfit', 'weightlifting', 'cardio', 'strength',
    'endurance', 'flexibility', 'nutrition', 'protein', 'recovery'
  ],
  6,
  true
),
(
  'Entertainment',
  ARRAY[
    'entertainment', 'movie', 'film', 'cinema', 'tv', 'television',
    'show', 'series', 'episode', 'season', 'actor', 'actress',
    'director', 'producer', 'music', 'song', 'album', 'artist',
    'concert', 'festival', 'performance', 'theater', 'play',
    'comedy', 'drama', 'action', 'thriller', 'horror', 'romance',
    'documentary', 'animation', 'cartoon', 'gaming', 'video game',
    'streaming', 'netflix', 'youtube', 'podcast', 'radio'
  ],
  5,
  true
),
(
  'Home & Garden',
  ARRAY[
    'home', 'house', 'apartment', 'room', 'kitchen', 'bedroom',
    'bathroom', 'living room', 'garden', 'yard', 'lawn', 'plants',
    'flowers', 'trees', 'gardening', 'landscaping', 'decoration',
    'interior design', 'furniture', 'appliances', 'renovation',
    'repair', 'maintenance', 'cleaning', 'organization', 'storage',
    'diy', 'tools', 'hardware', 'paint', 'wallpaper', 'flooring'
  ],
  4,
  true
),
(
  'Fashion & Beauty',
  ARRAY[
    'fashion', 'style', 'clothing', 'outfit', 'dress', 'shirt',
    'pants', 'shoes', 'accessories', 'jewelry', 'bag', 'beauty',
    'makeup', 'cosmetics', 'skincare', 'haircare', 'salon',
    'spa', 'manicure', 'pedicure', 'facial', 'massage', 'perfume',
    'fragrance', 'trend', 'designer', 'brand', 'shopping', 'boutique'
  ],
  4,
  true
),
(
  'Automotive',
  ARRAY[
    'car', 'vehicle', 'automobile', 'truck', 'motorcycle', 'bike',
    'driving', 'license', 'insurance', 'maintenance', 'repair',
    'mechanic', 'garage', 'engine', 'transmission', 'brake',
    'tire', 'oil', 'gas', 'fuel', 'electric vehicle', 'hybrid',
    'tesla', 'bmw', 'mercedes', 'toyota', 'honda', 'ford',
    'racing', 'speed', 'performance', 'modification', 'tuning'
  ],
  4,
  true
),
(
  'Real Estate',
  ARRAY[
    'real estate', 'property', 'house', 'home', 'apartment', 'condo',
    'buying', 'selling', 'renting', 'lease', 'mortgage', 'loan',
    'agent', 'broker', 'listing', 'market', 'price', 'value',
    'investment', 'rental', 'tenant', 'landlord', 'inspection',
    'appraisal', 'closing', 'contract', 'deed', 'title', 'equity'
  ],
  5,
  true
),
(
  'Legal',
  ARRAY[
    'legal', 'law', 'lawyer', 'attorney', 'court', 'judge', 'jury',
    'trial', 'case', 'lawsuit', 'litigation', 'contract', 'agreement',
    'patent', 'trademark', 'copyright', 'intellectual property',
    'criminal', 'civil', 'family law', 'divorce', 'custody',
    'immigration', 'tax law', 'corporate law', 'employment law',
    'personal injury', 'estate planning', 'will', 'trust'
  ],
  6,
  true
),
(
  'Photography',
  ARRAY[
    'photography', 'photo', 'camera', 'lens', 'dslr', 'mirrorless',
    'portrait', 'landscape', 'wedding', 'event', 'street photography',
    'macro', 'wildlife', 'nature', 'studio', 'lighting', 'flash',
    'exposure', 'aperture', 'shutter speed', 'iso', 'composition',
    'editing', 'photoshop', 'lightroom', 'raw', 'jpeg', 'print'
  ],
  5,
  true
),
(
  'Music',
  ARRAY[
    'music', 'song', 'album', 'artist', 'musician', 'singer',
    'band', 'orchestra', 'choir', 'instrument', 'guitar', 'piano',
    'violin', 'drums', 'bass', 'saxophone', 'trumpet', 'recording',
    'studio', 'mixing', 'mastering', 'concert', 'performance',
    'genre', 'rock', 'pop', 'jazz', 'classical', 'country', 'hip hop',
    'electronic', 'folk', 'blues', 'reggae', 'metal', 'punk'
  ],
  6,
  true
),
(
  'Gaming',
  ARRAY[
    'gaming', 'game', 'video game', 'console', 'pc gaming', 'mobile game',
    'playstation', 'xbox', 'nintendo', 'steam', 'esports', 'tournament',
    'streamer', 'twitch', 'youtube gaming', 'mmorpg', 'fps', 'rpg',
    'strategy', 'puzzle', 'action', 'adventure', 'simulation', 'racing',
    'fighting', 'sports game', 'indie game', 'aaa', 'developer',
    'publisher', 'beta', 'alpha', 'release', 'patch', 'update', 'dlc'
  ],
  5,
  true
),
(
  'Pets & Animals',
  ARRAY[
    'pet', 'animal', 'dog', 'cat', 'bird', 'fish', 'rabbit', 'hamster',
    'guinea pig', 'reptile', 'snake', 'lizard', 'turtle', 'horse',
    'veterinarian', 'vet', 'grooming', 'training', 'feeding', 'care',
    'adoption', 'rescue', 'shelter', 'breed', 'puppy', 'kitten',
    'wildlife', 'zoo', 'aquarium', 'farm', 'livestock', 'chicken'
  ],
  4,
  true
),
(
  'Books & Literature',
  ARRAY[
    'book', 'novel', 'story', 'literature', 'reading', 'author',
    'writer', 'poet', 'poetry', 'fiction', 'non-fiction', 'biography',
    'memoir', 'autobiography', 'essay', 'short story', 'play',
    'screenplay', 'script', 'publishing', 'publisher', 'editor',
    'library', 'bookstore', 'bestseller', 'award', 'review', 'critique',
    'genre', 'mystery', 'thriller', 'romance', 'fantasy', 'sci-fi'
  ],
  5,
  true
),
(
  'Parenting & Family',
  ARRAY[
    'parenting', 'parent', 'mother', 'father', 'mom', 'dad', 'child',
    'baby', 'toddler', 'teenager', 'family', 'pregnancy', 'birth',
    'childcare', 'daycare', 'school', 'education', 'development',
    'milestone', 'feeding', 'sleeping', 'discipline', 'behavior',
    'activities', 'playtime', 'toys', 'safety', 'health', 'pediatric'
  ],
  5,
  true
),
(
  'Relationships',
  ARRAY[
    'relationship', 'dating', 'love', 'romance', 'marriage', 'wedding',
    'engagement', 'couple', 'partner', 'boyfriend', 'girlfriend',
    'husband', 'wife', 'anniversary', 'valentine', 'friendship',
    'social', 'communication', 'trust', 'commitment', 'intimacy',
    'conflict', 'resolution', 'therapy', 'counseling', 'advice'
  ],
  4,
  true
),
(
  'Spirituality & Religion',
  ARRAY[
    'spirituality', 'religion', 'faith', 'belief', 'prayer', 'meditation',
    'church', 'temple', 'mosque', 'synagogue', 'worship', 'service',
    'bible', 'quran', 'torah', 'scripture', 'holy', 'sacred',
    'god', 'divine', 'soul', 'spirit', 'enlightenment', 'peace',
    'mindfulness', 'gratitude', 'blessing', 'miracle', 'hope'
  ],
  4,
  true
),
(
  'Environment & Sustainability',
  ARRAY[
    'environment', 'sustainability', 'eco-friendly', 'green', 'renewable',
    'solar', 'wind', 'energy', 'climate change', 'global warming',
    'carbon footprint', 'recycling', 'waste', 'pollution', 'conservation',
    'biodiversity', 'ecosystem', 'organic', 'natural', 'earth day',
    'environmental protection', 'clean energy', 'sustainable living',
    'zero waste', 'composting', 'reusable', 'electric vehicle'
  ],
  6,
  true
),
(
  'Mental Health',
  ARRAY[
    'mental health', 'psychology', 'therapy', 'counseling', 'psychiatry',
    'depression', 'anxiety', 'stress', 'ptsd', 'bipolar', 'adhd',
    'autism', 'ocd', 'panic', 'phobia', 'trauma', 'grief', 'loss',
    'self-care', 'mindfulness', 'meditation', 'relaxation', 'coping',
    'support', 'group therapy', 'medication', 'antidepressant', 'wellness'
  ],
  9,
  true
),
(
  'Hobbies & Crafts',
  ARRAY[
    'hobby', 'craft', 'diy', 'handmade', 'knitting', 'crochet', 'sewing',
    'quilting', 'embroidery', 'woodworking', 'pottery', 'ceramics',
    'painting', 'drawing', 'sketching', 'scrapbooking', 'jewelry making',
    'candle making', 'soap making', 'origami', 'calligraphy', 'collecting',
    'model building', 'puzzle', 'board game', 'card game', 'chess'
  ],
  3,
  true
);

-- Verify the insertion
SELECT tag_name, array_length(keywords, 1) as keyword_count, priority 
FROM tag_detection_rules 
ORDER BY priority DESC;
