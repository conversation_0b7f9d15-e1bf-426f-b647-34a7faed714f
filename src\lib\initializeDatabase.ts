import { supabase } from './supabase';

// Function to create the tag_detection_rules table if it doesn't exist
export const createTagDetectionRulesTable = async (): Promise<boolean> => {
  try {
    console.log('Creating tag_detection_rules table...');
    
    // Create the table
    const { error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tag_detection_rules (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          tag_name VARCHAR(100) NOT NULL,
          keywords TEXT[] NOT NULL,
          priority INTEGER NOT NULL DEFAULT 1,
          is_active BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (tableError) {
      console.error('Error creating table:', tableError);
      return false;
    }

    // Create indexes
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_active ON tag_detection_rules(is_active);
        CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_priority ON tag_detection_rules(priority DESC);
        CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_tag_name ON tag_detection_rules(tag_name);
      `
    });

    if (indexError) {
      console.error('Error creating indexes:', indexError);
      // Don't return false here as the table was created successfully
    }

    console.log('tag_detection_rules table created successfully');
    return true;
  } catch (error) {
    console.error('Error in createTagDetectionRulesTable:', error);
    return false;
  }
};

// Alternative approach: Check if table exists and create via direct SQL
export const ensureTagDetectionRulesTable = async (): Promise<boolean> => {
  try {
    // Try to query the table to see if it exists
    const { error } = await supabase
      .from('tag_detection_rules')
      .select('id')
      .limit(1);

    if (error) {
      // If error contains "relation does not exist", the table doesn't exist
      if (error.message.includes('relation') && error.message.includes('does not exist')) {
        console.log('tag_detection_rules table does not exist. Please create it manually in Supabase.');
        console.log('SQL to run in Supabase SQL Editor:');
        console.log(`
CREATE TABLE IF NOT EXISTS tag_detection_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tag_name VARCHAR(100) NOT NULL,
  keywords TEXT[] NOT NULL,
  priority INTEGER NOT NULL DEFAULT 1,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_active ON tag_detection_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_priority ON tag_detection_rules(priority DESC);
CREATE INDEX IF NOT EXISTS idx_tag_detection_rules_tag_name ON tag_detection_rules(tag_name);
        `);
        return false;
      } else {
        console.error('Error checking table existence:', error);
        return false;
      }
    }

    console.log('tag_detection_rules table exists');
    return true;
  } catch (error) {
    console.error('Error in ensureTagDetectionRulesTable:', error);
    return false;
  }
};
