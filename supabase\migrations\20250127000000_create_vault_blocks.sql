/*
  # Create vault_blocks table for storing user's knowledge blocks

  1. New Tables
    - `vault_blocks`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users table)
      - `name` (text, not null) - block title
      - `smart_tag` (text) - category/tag for the block
      - `banner_gradient` (text) - CSS gradient class
      - `custom_banner_image` (text) - URL or base64 image data
      - `is_pinned` (boolean, default false)
      - `total_items` (integer, default 0) - count of items in block
      - `position` (integer) - for ordering blocks
      - `created_at` (timestamp, default now)
      - `updated_at` (timestamp, default now)

  2. Security
    - Enable RLS on `vault_blocks` table
    - Add policy for users to manage their own blocks
*/

-- Create vault_blocks table
CREATE TABLE IF NOT EXISTS vault_blocks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  parent_id uuid REFERENCES vault_blocks(id) ON DELETE CASCADE,
  name text NOT NULL,
  smart_tag text DEFAULT 'General',
  banner_gradient text DEFAULT 'from-red-300 to-white',
  custom_banner_image text,
  is_pinned boolean DEFAULT false,
  total_items integer DEFAULT 0,
  position integer DEFAULT 0,
  depth integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE vault_blocks ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own vault blocks
CREATE POLICY "Users can manage own vault blocks"
  ON vault_blocks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at
CREATE TRIGGER update_vault_blocks_updated_at
  BEFORE UPDATE ON vault_blocks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_vault_blocks_user_id ON vault_blocks(user_id);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_parent_id ON vault_blocks(parent_id);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_position ON vault_blocks(user_id, position);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_is_pinned ON vault_blocks(user_id, is_pinned);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_depth ON vault_blocks(user_id, depth);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_hierarchy ON vault_blocks(user_id, parent_id, position);
CREATE INDEX IF NOT EXISTS idx_vault_blocks_created_at ON vault_blocks(user_id, created_at DESC);

-- Create block_contents table for storing content within blocks
CREATE TABLE IF NOT EXISTS block_contents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  block_id uuid NOT NULL REFERENCES vault_blocks(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text NOT NULL,
  content_type text DEFAULT 'text' CHECK (content_type IN ('text', 'image', 'video', 'audio', 'file')),
  position integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for block_contents
ALTER TABLE block_contents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own block contents
CREATE POLICY "Users can manage own block contents"
  ON block_contents
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at for block_contents
CREATE TRIGGER update_block_contents_updated_at
  BEFORE UPDATE ON block_contents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for block_contents
CREATE INDEX IF NOT EXISTS idx_block_contents_block_id ON block_contents(block_id);
CREATE INDEX IF NOT EXISTS idx_block_contents_user_id ON block_contents(user_id);
CREATE INDEX IF NOT EXISTS idx_block_contents_position ON block_contents(block_id, position);

-- Create user_settings table for storing user preferences
CREATE TABLE IF NOT EXISTS user_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
  vault_name text DEFAULT 'My Vault',
  vault_banner_image text,
  theme text DEFAULT 'dark-crimson',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for user_settings
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own settings
CREATE POLICY "Users can manage own settings"
  ON user_settings
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at for user_settings
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for user_settings
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
