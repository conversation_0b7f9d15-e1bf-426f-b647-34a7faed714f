-- Create block_contents table for storing content within blocks
CREATE TABLE IF NOT EXISTS public.block_contents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  block_id uuid NOT NULL REFERENCES public.vault_blocks(id) ON DELETE CASCADE,
  user_id text NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text NOT NULL,
  content_type text DEFAULT 'text' CHECK (content_type IN ('text', 'image', 'video', 'audio', 'file')),
  position integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for block_contents
ALTER TABLE public.block_contents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own block contents
CREATE POLICY "Users can manage own block contents"
  ON public.block_contents
  FOR ALL
  TO authenticated
  USING (auth.uid()::text = user_id);

-- Function to automatically update updated_at timestamp (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at for block_contents
CREATE TRIGGER update_block_contents_updated_at
  BEFORE UPDATE ON public.block_contents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for block_contents
CREATE INDEX IF NOT EXISTS idx_block_contents_block_id ON public.block_contents(block_id);
CREATE INDEX IF NOT EXISTS idx_block_contents_user_id ON public.block_contents(user_id);
CREATE INDEX IF NOT EXISTS idx_block_contents_position ON public.block_contents(block_id, position);
CREATE INDEX IF NOT EXISTS idx_block_contents_created_at ON public.block_contents(created_at DESC);
