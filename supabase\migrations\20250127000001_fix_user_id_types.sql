/*
  # Fix user_id type mismatch in vault_blocks, block_contents, and user_settings tables
  
  This migration fixes the type mismatch where user_id was defined as text 
  but should be uuid to properly reference the users table.
*/

-- First, drop existing foreign key constraints and policies
DROP POLICY IF EXISTS "Users can manage own vault blocks" ON vault_blocks;
DROP POLICY IF EXISTS "Users can manage own block contents" ON block_contents;
DROP POLICY IF EXISTS "Users can manage own settings" ON user_settings;

-- Drop existing triggers
DROP TRIGGER IF EXISTS update_vault_blocks_updated_at ON vault_blocks;
DROP TRIGGER IF EXISTS update_block_contents_updated_at ON block_contents;
DROP TRIGGER IF EXISTS update_user_settings_updated_at ON user_settings;

-- Drop existing tables to recreate with correct types
DROP TABLE IF EXISTS block_contents;
DROP TABLE IF EXISTS vault_blocks;
DROP TABLE IF EXISTS user_settings;

-- Recreate vault_blocks table with correct user_id type
CREATE TABLE vault_blocks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  parent_id uuid REFERENCES vault_blocks(id) ON DELETE CASCADE,
  name text NOT NULL,
  smart_tag text DEFAULT 'General',
  banner_gradient text DEFAULT 'from-red-300 to-white',
  custom_banner_image text,
  is_pinned boolean DEFAULT false,
  total_items integer DEFAULT 0,
  position integer DEFAULT 0,
  depth integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE vault_blocks ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own vault blocks
CREATE POLICY "Users can manage own vault blocks"
  ON vault_blocks
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at
CREATE TRIGGER update_vault_blocks_updated_at
  BEFORE UPDATE ON vault_blocks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_vault_blocks_user_id ON vault_blocks(user_id);
CREATE INDEX idx_vault_blocks_parent_id ON vault_blocks(parent_id);
CREATE INDEX idx_vault_blocks_position ON vault_blocks(user_id, position);
CREATE INDEX idx_vault_blocks_is_pinned ON vault_blocks(user_id, is_pinned);
CREATE INDEX idx_vault_blocks_depth ON vault_blocks(user_id, depth);
CREATE INDEX idx_vault_blocks_hierarchy ON vault_blocks(user_id, parent_id, position);
CREATE INDEX idx_vault_blocks_created_at ON vault_blocks(user_id, created_at DESC);

-- Recreate block_contents table with correct user_id type
CREATE TABLE block_contents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  block_id uuid NOT NULL REFERENCES vault_blocks(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text NOT NULL,
  content_type text DEFAULT 'text' CHECK (content_type IN ('text', 'image', 'video', 'audio', 'file')),
  position integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for block_contents
ALTER TABLE block_contents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own block contents
CREATE POLICY "Users can manage own block contents"
  ON block_contents
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at for block_contents
CREATE TRIGGER update_block_contents_updated_at
  BEFORE UPDATE ON block_contents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for block_contents
CREATE INDEX idx_block_contents_block_id ON block_contents(block_id);
CREATE INDEX idx_block_contents_user_id ON block_contents(user_id);
CREATE INDEX idx_block_contents_position ON block_contents(block_id, position);

-- Recreate user_settings table with correct user_id type
CREATE TABLE user_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
  vault_name text DEFAULT 'My Vault',
  vault_banner_image text,
  theme text DEFAULT 'dark-crimson',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security for user_settings
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Policy: Users can manage their own settings
CREATE POLICY "Users can manage own settings"
  ON user_settings
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to automatically update updated_at for user_settings
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for user_settings
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
