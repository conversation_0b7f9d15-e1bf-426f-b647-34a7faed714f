<!DOCTYPE html>
<html>
<head>
    <title>Test Supabase Function</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Supabase Function</h1>
    <button onclick="createFunction()">Apply Migration</button>
    <button onclick="testFunction()">Test Insert</button>
    <div id="output"></div>

    <script>
        const supabaseUrl = 'https://zyijbgtqthkbbtprkkod.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5aWpiZ3RxdGhrYmJ0cHJra29kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0Mzg1MTAsImV4cCI6MjA2OTAxNDUxMH0.jHpQv8AKlBcsl3B85et6mc4R5U6Z-1co-NK6eDc8upg';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);

        async function createFunction() {
            const migrationSQL = `
-- Drop existing restrictive policy
DROP POLICY IF EXISTS "Users can manage own block contents" ON block_contents;

-- Create new policies that work with Firebase authentication
-- Policy for authenticated Supabase users (existing functionality)
CREATE POLICY "Authenticated users can manage own block contents"
  ON block_contents
  FOR ALL
  TO authenticated
  USING (auth.uid()::text = user_id);

-- Policy for anonymous users (Firebase authenticated) - SELECT
CREATE POLICY "Anonymous users can read own block contents"
  ON block_contents
  FOR SELECT
  TO anon
  USING (
    user_id IN (
      SELECT id FROM users WHERE id = user_id
    )
  );

-- Policy for anonymous users (Firebase authenticated) - INSERT
CREATE POLICY "Anonymous users can insert own block contents"
  ON block_contents
  FOR INSERT
  TO anon
  WITH CHECK (
    -- User must exist in users table
    user_id IN (SELECT id FROM users WHERE id = user_id)
    AND
    -- Block must exist and belong to the user
    block_id IN (
      SELECT id FROM vault_blocks
      WHERE id = block_id AND user_id = block_contents.user_id
    )
  );

-- Policy for anonymous users (Firebase authenticated) - UPDATE
CREATE POLICY "Anonymous users can update own block contents"
  ON block_contents
  FOR UPDATE
  TO anon
  USING (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  )
  WITH CHECK (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  );

-- Policy for anonymous users (Firebase authenticated) - DELETE
CREATE POLICY "Anonymous users can delete own block contents"
  ON block_contents
  FOR DELETE
  TO anon
  USING (
    user_id IN (SELECT id FROM users WHERE id = user_id)
  );
            `;

            try {
                const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
                document.getElementById('output').innerHTML = `<pre>Apply Migration Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }

        async function testFunction() {
            try {
                const { data, error } = await supabase
                    .from('block_contents')
                    .insert([{
                        block_id: '00000000-0000-0000-0000-000000000000',
                        user_id: 'test-user',
                        title: 'test.txt',
                        content: 'test content',
                        content_type: 'text',
                        position: 0
                    }])
                    .select()
                    .single();
                document.getElementById('output').innerHTML = `<pre>Test Insert Result: ${JSON.stringify({ data, error }, null, 2)}</pre>`;
            } catch (err) {
                document.getElementById('output').innerHTML = `<pre>Error: ${err.message}</pre>`;
            }
        }
    </script>
</body>
</html>
